<!DOCTYPE html>
<html>

<head>
    <meta name="viewport" charset="utf-8"
        content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />

    <!-- 禁用快取 -->
    <meta http-equiv="Expires" content="0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Cache-control" content="no-cache">
    <meta http-equiv="Cache" content="no-cache">

    <!-- 其他必要lib -->
    <script src="../../lib/ThirdPartyTool.js" charset="utf-8" async></script>
    <link rel=stylesheet type="text/css" href="./history.css" charset="utf-8">

    <script>
        document.oncontextmenu = new Function("return false");
        oncontextmenu = "return false;"
    </script>
</head>

<body onload="HistoryPage.doLoad()">

    <!-- 主要程式邏輯 -->
    <script src="history.js" charset="utf-8"></script>
    <script src="../chat/chatEvent.js" charset="utf-8"></script>

    <div id="App">
        <!-- 頂部區塊 (可自行調整) -->
        <nav class="navbar navbar-light justify-content-center navBar">
            <font class="title">
                {{T['historyTitle']}}
            </font>
        </nav>
        <button class="col col-12 ChatAddBtn" v-on:click="cleanMessageAndShowGreeting()">
            <img src="../../image/icon.png" alt="" width="20px">&nbsp;&nbsp;{{T['createNewChat']}}
        </button>
        <!-- 歷史紀錄容器 -->
        <div class="historyPanel">
            <ul class="historyList">
                <!-- 今天 -->
                <div v-if="categorizedHistory['今天'].length">
                    <li class="categorizeText" v-if="categorizedHistory['今天'].length > 0">今天</li>
                    <li v-for="item in categorizedHistory['今天']" :key="item.FId" class="historyItem" @mouseleave="handleMouseLeave" :class="{ historyItemClicked: historyNowId === item.FId }">
                        <div v-if="editingId !== item.FId" class="historyItemContent" @click="doGetHistoryData(item.FId)">
                            <div class="textWithMenu">
                            <span class="itemText" :title="item.FName">{{ item.FName }}</span>
                            <button class="dotsBtn" @click.stop="toggleMenu(item.FId)">⋯</button>
                            </div>
                            <div class="itemMenu" v-if="showMenuId === item.FId">
                            <div class="menuItem" @click="startRename(item.FId)">{{T['roomReNameText']}}</div>
                            <div class="menuItem" v-if="!item.isCleanChat" @click.stop="deleteItem(item.FId)">{{T['roomDeleteText']}}</div>
                            </div>
                        </div>
                        <div v-else class="historyItemEdit">
                            <input v-focus v-model="tempName" @blur="applyRename(item.FId)" @keyup.enter="applyRename(item.FId)" />
                        </div>
                    </li>
                </div>

                <!-- 過去 7 天 -->
                <div v-if="categorizedHistory['過去 7 天'].length">
                    <li class="categorizeText">過去 7 天</li>
                    <li v-for="item in categorizedHistory['過去 7 天']" :key="item.FId" class="historyItem" @mouseleave="handleMouseLeave" :class="{ historyItemClicked: historyNowId === item.FId }">
                        <div v-if="editingId !== item.FId" class="historyItemContent" @click="doGetHistoryData(item.FId)">
                            <div class="textWithMenu">
                            <span class="itemText" :title="item.FName">{{ item.FName }}</span>
                            <button class="dotsBtn" @click.stop="toggleMenu(item.FId)">⋯</button>
                            </div>
                            <div class="itemMenu" v-if="showMenuId === item.FId">
                            <div class="menuItem" @click="startRename(item.FId)">{{T['roomReNameText']}}</div>
                            <div class="menuItem" @click.stop="deleteItem(item.FId)">{{T['roomDeleteText']}}</div>
                            </div>
                        </div>
                        <div v-else class="historyItemEdit">
                            <input v-focus v-model="tempName" @blur="applyRename(item.FId)" @keyup.enter="applyRename(item.FId)" />
                        </div>
                    </li>
                </div>

                <!-- 過去 30 天 -->
                <div v-if="categorizedHistory['過去 30 天'].length">
                    <li class="categorizeText">過去 30 天</li>
                    <li v-for="item in categorizedHistory['過去 30 天']" :key="item.FId" class="historyItem" @mouseleave="handleMouseLeave" :class="{ historyItemClicked: historyNowId === item.FId }">
                        <div v-if="editingId !== item.FId" class="historyItemContent" @click="doGetHistoryData(item.FId)">
                            <div class="textWithMenu">
                            <span class="itemText" :title="item.FName">{{ item.FName }}</span>
                            <button class="dotsBtn" @click.stop="toggleMenu(item.FId)">⋯</button>
                            </div>
                            <div class="itemMenu" v-if="showMenuId === item.FId">
                            <div class="menuItem" @click="startRename(item.FId)">{{T['roomReNameText']}}</div>
                            <div class="menuItem" @click.stop="deleteItem(item.FId)">{{T['roomDeleteText']}}</div>
                            </div>
                        </div>
                        <div v-else class="historyItemEdit">
                            <input v-focus v-model="tempName" @blur="applyRename(item.FId)" @keyup.enter="applyRename(item.FId)" />
                        </div>
                    </li>
                </div>

                <!-- 月份 -->
                <div v-for="(items, label) in categorizedHistory['月份']" :key="label">
                    <li class="categorizeText">{{ label }}</li>
                    <li v-for="item in items" :key="item.FId" class="historyItem" @mouseleave="handleMouseLeave" :class="{ historyItemClicked: historyNowId === item.FId }">
                        <div v-if="editingId !== item.FId" class="historyItemContent" @click="doGetHistoryData(item.FId)">
                            <div class="textWithMenu">
                            <span class="itemText" :title="item.FName">{{ item.FName }}</span>
                            <button class="dotsBtn" @click.stop="toggleMenu(item.FId)">⋯</button>
                            </div>
                            <div class="itemMenu" v-if="showMenuId === item.FId">
                            <div class="menuItem" @click="startRename(item.FId)">{{T['roomReNameText']}}</div>
                            <div class="menuItem" @click.stop="deleteItem(item.FId)">{{T['roomDeleteText']}}</div>
                            </div>
                        </div>
                        <div v-else class="historyItemEdit">
                            <input v-focus v-model="tempName" @blur="applyRename(item.FId)" @keyup.enter="applyRename(item.FId)" />
                        </div>
                    </li>
                </div>

                <!-- 年份 -->
                <div v-for="(items, label) in categorizedHistory['年份']" :key="label">
                    <li class="categorizeText">{{ label }}</li>
                    <li v-for="item in items" :key="item.FId" class="historyItem" @mouseleave="handleMouseLeave" :class="{ historyItemClicked: historyNowId === item.FId }">
                        <div v-if="editingId !== item.FId" class="historyItemContent" @click="doGetHistoryData(item.FId)">
                            <div class="textWithMenu">
                            <span class="itemText" :title="item.FName">{{ item.FName }}</span>
                            <button class="dotsBtn" @click.stop="toggleMenu(item.FId)">⋯</button>
                            </div>
                            <div class="itemMenu" v-if="showMenuId === item.FId">
                            <div class="menuItem" @click="startRename(item.FId)">{{T['roomReNameText']}}</div>
                            <div class="menuItem" @click.stop="deleteItem(item.FId)">{{T['roomDeleteText']}}</div>
                            </div>
                        </div>
                        <div v-else class="historyItemEdit">
                            <input v-focus v-model="tempName" @blur="applyRename(item.FId)" @keyup.enter="applyRename(item.FId)" />
                        </div>
                    </li>
                </div>
                <div v-if="isHistoryListLoading" class="loadingIndicator">
                    <img src="../../image/Loading.gif" style="width: 50px" alt="載入中..." />
                  </div>
            </ul>
        </div>
    </div>
</body>

</html>
