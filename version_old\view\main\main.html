<!DOCTYPE html>
<html>

<head>
    <meta name="viewport" charset="utf-8"
        content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />

    <!-- 禁用快取 -->
    <meta http-equiv="Expires" content="0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Cache-control" content="no-cache">
    <meta http-equiv="Cache" content="no-cache">

    <!-- 載入必要lib -->
    <script src="../../lib/ThirdPartyTool.js" charset="utf-8" async></script>
    <!-- 登入模組 -->
    <script src="../../ecp/EcpMemberLogin.js" charset="utf-8"></script>
    <script src="../../ecp/EcpUserLogin.js" charset="utf-8"></script>

    <script src="../../lib/vconsole/vconsole.min.js"></script>

    <script>
        document.oncontextmenu = new Function("return false");
        oncontextmenu = "return false;"
    </script>
</head>

<body onload="Main.doLoad()">

    <!-- 主要程式邏輯 -->
    <script src="main.js" charset="utf-8"></script>
    <script src="./viewController.js" charset="utf-8"></script>
    <link rel=stylesheet type="text/css" href="./main.css" charset="utf-8">

    <div id="App">
        <nav class="navbar navbar-light justify-content-center navBar">
            <a class="navbar-brand backIcon" v-on:click="doToggle"><img class="mainbackIcon"
                    src="../../image/previous.png"></a>
            <a id="historyIcon" v-bind:title="T['historyTitle']" class="navbar-brand historytoggleIcon" v-if="UserInfo['userMode']!='anonymous'" v-on:click="doHistoryToggle()"><img class="mainhistorytoggleIcon"
                    src="../../image/historyToggle.png" ></a>            
            <!-- <a id="historyIcon" v-bind:title="T['historyTitle']" class="navbar-brand historytoggleIcon" v-if="false" v-on:click="doHistoryToggle()"><img class="mainhistorytoggleIcon"
                    src="../../image/historyToggle.png" ></a> -->
            <font class="title" v-bind:title="isTeamsChannel ? '' : T['pinCopilot']" v-on:click="doTogglePin"
                style="cursor: pointer;user-select:none; ">
                {{T['title']}}
                <img class="pin" src="../../image/pin.png" width="20px" v-if="pin && !isTeamsChannel">
                <img src="../../image/pin_disable.png" width="20px" v-if="!pin && !isTeamsChannel">
            </font>
            <a id="mainFontSize" class="mainFontSize" v-on:click="switchFontSize(false)"><img class="mainFontSizeIcon"
                    src="../../image/font_size.png"></a>
            <a id="userIcon" class="userIcon" v-on:click="openUserInfo" v-bind:title="T['userIconHint']"> <img
                    onerror="this.src='../../image/user.png'" v-bind:src="UserInfo['userIcon']" width="35px"
                    style="border-radius:30px" v-if="!isCustomCssEnable"></a>
            <a id="userIcon" class="userIcon" v-on:click="openUserInfo" v-bind:title="T['userIconHint']"> <img
                    onerror="this.src='../../image/user_custom.png'" v-bind:src="UserInfo['userIcon']" width="35px"
                    style="border-radius:30px" v-if="isCustomCssEnable"></a>
        </nav>
        <iframe class="mainFrame" id="mainFrame"></iframe>
        <!-- Modal -->
        <div class="modal fade" id="accountView" tabindex="-1" role="dialog" aria-labelledby="accountView"
            aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="accountView">{{T['userInfo']}}</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div>
                            <img v-if="!isCustomCssEnable" onerror="this.src='../../image/user.png'"
                                v-bind:src="UserInfo['userIcon']" width="40px" style="border-radius:30px">
                            <img v-if="isCustomCssEnable" onerror="this.src='../../image/user_custom.png'"
                                v-bind:src="UserInfo['userIcon']" width="40px" style="border-radius:30px">
                            <font>{{userTypeContent}}</font>
                            <font>{{UserInfo['userName']}}</font>
                            <a href="#" v-if="showLogout && !isTeamsChannel" v-on:click="doLogout()">{{T['logout']}}</a>
                            <a href="#" v-if="showLeave && !isTeamsChannel" v-on:click="doLogout()">{{T['leave']}}</a>

                            <hr v-if="!isMobile">
                            <div v-if="!isMobile && !isTeamsChannel">
                                <font>{{T['displaySize']}}</font>
                                <div class="btn-group btn-group-sm" role="group" aria-label="Basic example">
                                    <button type="button" v-bind:class="size === 'l'?'btn btn-info':'btn btn-light'"
                                        v-on:click="doChangeSize('l')">{{T['displayBig']}}</button>
                                    <button type="button" v-bind:class="size === 'm'?'btn btn-info':'btn btn-light'"
                                        v-on:click="doChangeSize('m')">{{T['displayNormal']}}</button>
                                    <button type="button" v-bind:class="size === 's'?'btn btn-info':'btn btn-light'"
                                        v-on:click="doChangeSize('s')">{{T['displaySmall']}}</button>
                                </div>
                            </div>
                            <div v-if="showAutoSpeech">
                                <font>{{T['webSpeechSynthesis']}}</font>
                                <div class="btn-group btn-group-sm" role="group" style="padding: 10px;"
                                    aria-label="Basic example">
                                    <button type="button"
                                        v-bind:class="AutoSpeech === true ?'btn btn-info':'btn btn-light'"
                                        v-on:click="switchAutoSpeech()">{{T['ON']}}</button>
                                    <button type="button"
                                        v-bind:class="AutoSpeech === false?'btn btn-info':'btn btn-light'"
                                        v-on:click="switchAutoSpeech()">{{T['OFF']}}</button>
                                </div>
                            </div>
                            <div class="version">
                                <font>{{version}}</font>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="loading" class="progressDiv animate__animated animate__fadeIn">
            <div>
                <font style="color: gray;">{{T['please_wait']}}</font>
                <div class="progress">
                    <div class="progressBar progress-bar progress-bar-striped progress-bar-animated" role="progressbar"
                        v-bind:aria-valuenow="progress" aria-valuemin="0" aria-valuemax="100"
                        v-bind:style="{width:progress+'%'}">
                        {{progress}} %
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>