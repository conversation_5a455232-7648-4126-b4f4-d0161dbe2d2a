.navBar {
    background-color: rgb(139, 168, 217);
}
.backIcon {
    position: absolute;
    left: 5px;
    cursor: pointer;
}
.mainbackIcon {
    width:30px;
}
.historytoggleIcon {
    position: absolute;
    left: 45px;
    cursor: pointer;
}
.mainhistorytoggleIcon {
    width:26px;
}

.userIcon {
    position: fixed;
    right: 5px;
    cursor: pointer;
}

.title {
    font-size: 1rem;
    color: white;
    font-weight: bold;
}

.mainFrame {
    width: 100%;
    height: calc(100vh - 40px);
    border: 0px;
}

.version {
    text-align: right;
    font-size: 10px;
    color: gray
}

body {
    overflow: hidden;
}
.pin{
    width: 20px;
}

.mainFontSize{
    position: absolute;
    right: 50px;
}

.mainFontSizeIcon{
    width:25px; 
    cursor: pointer;
}

.mainFontSize:hover{
    background-color: rgb(185, 209, 251);
    border-radius: 5px;
}