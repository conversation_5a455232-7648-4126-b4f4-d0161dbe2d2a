var EcpController = {

    BASE_URL: "../../ecp/KMCopilot",
    MODE: "ECP", //ECP、PLUGIN

    KM_COPILOT_ID: "KMCopilotMainView",
    KM_COPILOT_WEB_ID: "KMCopilotMainWeb",
    KM_COPILOT_HISTORY_ID: "KMCopilotMainHistory",
    KM_COPILOT_ICON_ID: "KMCopilotMainIcon",
    SHOW_TOGGLE: false,
    SHOW_HISTORYTOGGLE:false,
    SHOW_WEB_TOGGLE: false,

    //Dragging
    isDragging: false,
    offsetX: 0,
    offsetY: 0,

    //QS強制使用Copilot
    qsEnableCopilot: false,

    //是否釘選
    pin: true,

    //目前大小
    NOW_SIZE: 's',

    //當前來源
    NOW_SOURCE: 'default',

    //語系判斷參數
    ecp_lang: "ecp_lang",
    
    i18n_langMap: {
        "zh-tw": "zh-TW",
        "en-us": "en-US",
        "ja-jp": "ja-JP",
        "zh-jp": "ja-<PERSON>",//QS問題
        "zh-cn": "zh-CN"
    },
    doLoad() {
        console.log('[EcpController doLoad] start.');
        this.doInitConfig();
        this.initBaseUrl();
        this.initCss(EcpController.BASE_URL + '/lib/wow/animate.min.css');
        this.initJs(EcpController.BASE_URL + '/lib/jquery/jquery-3.3.1.min.js');
        this.initIcon();
        this.initLanguage();
        this.initKMCopilot();
        this.initKMWebView();
        this.initKMHistory();
        this.initToggle();
        this.doMoveEventInit();
        console.log('[EcpController doLoad] finish.');
    },

    // 取得網址參數
    getURLParameter(queryString, paramater) {
        let urlParams = new URLSearchParams(queryString);
        return urlParams.get(paramater);
    },

    doInitConfig() {
        if (typeof Config !== 'undefined') {
            EcpController.qsEnableCopilot = Config.QS_ENABLE_COPILOT;
        }
        localStorage.setItem('qsEnableCopilot', EcpController.qsEnableCopilot);
    },

    initBaseUrl() {
        try {
            if (!parent.parent.Jui) {
                let pluginUrl = document.getElementById("CopilotPlugIn").getAttribute("src");
                EcpController.BASE_URL = pluginUrl.substring(0, pluginUrl.indexOf("/ecp"));
                EcpController.MODE = "PLUGIN";
            }
        } catch (e) {
            EcpController.BASE_URL = '.';
            EcpController.MODE = "PLUGIN";
            console.log('[EcpController initBaseUrl] use default.');
        }
        if (EcpController.qsEnableCopilot) {
            EcpController.MODE = "PLUGIN";
            EcpController.BASE_URL = Config.COPILOT_BASE_URL;
            console.log('[EcpController initBaseUrl] base on QS Mode.');
        }
        localStorage.setItem('QbiCopilotMode', EcpController.MODE);
    },

    initToggle() {
        EcpController.SHOW_TOGGLE = false;
    },

    initCss(path) {
        let script = document.createElement('link');
        script.setAttribute('href', path);
        script.setAttribute('rel', 'stylesheet');
        script.setAttribute('media', 'all');
        document.head.appendChild(script);
    },

    initJs: function (path) {
        let script = document.createElement('script');
        script.setAttribute('src', path);
        document.head.appendChild(script);
    },

    initIcon() {
        let img = document.createElement("img");
        img.id = EcpController.KM_COPILOT_ICON_ID;
        img.src = EcpController.BASE_URL + '/image/icon.png';
        img.draggable = "true";
        img.style.height = "55px";
        img.style.width = "55px";
        img.style.position = "fixed";
        img.style.bottom = "40px";
        img.style.right = "30px";
        img.style.zIndex = "999";
        img.style.cursor = "pointer";
        img.style.display = "none";
        img.style.filter = "drop-shadow(8px 8px 8px rgba(0, 0, 0, 0.4))";
        img.setAttribute("class", "animate__animated animate__rubberBand");
        img.addEventListener("click", function (e) {
            EcpController.ICON_CLICK = true;
            EcpController.doToggle();
        })
        document.body.appendChild(img);

        let source = EcpController.getURLParameter(location.href, "source");
        source = source == null ? 'default' : source;
        console.log('[EcpController initIcon] source = ' + source);
        EcpController.NOW_SOURCE = source;
        localStorage.setItem('source', source);
        if ('teams' == source) {
            setTimeout(function () {
                document.getElementById(EcpController.KM_COPILOT_ICON_ID).style.display = '';
            }, 500);
        }
    },
    initLanguage(){
        if (parent.parent.Jui) {
            //內嵌判讀ECP選擇語系
            if(!!!sessionStorage.getItem("language")){
                sessionStorage.setItem("language", EcpController.ecp_lang);
            }
        }else{
            //外嵌URL語系判讀
            if(!!!sessionStorage.getItem("languageChange")){
                let language = new URLSearchParams(window.location.search).get("language")?.toLowerCase();
                language = parent.parent.EcpController.i18n_langMap[language];
                if(language)sessionStorage.setItem("language", language);
            }
        }
    },
    initKMCopilot() {
        let iframe = document.createElement("iframe");
        iframe.setAttribute("src", EcpController.BASE_URL + '/view/main/main.html');
        iframe.setAttribute("frameBorder", "0");
        iframe.id = EcpController.KM_COPILOT_ID;
        iframe.style.height = this._isMobile() ? "98%" : EcpController.MODE == 'ECP' ? "calc(100vh - 0px)" : "calc(100vh - 10px)";
        iframe.style.width = this._isMobile() ? "98%" : "calc(43vh - 0px)";
        iframe.style.zIndex = "999";
        iframe.style.position = "fixed";
        iframe.style.bottom = "0px";
        iframe.style.right = "0px";
        iframe.style.borderRadius = "10px";
        iframe.style.display = "none";
        iframe.style.backdropFilter = "blur(5px)";
        iframe.style.padding = "5px";
        iframe.style.minWidth = this._isMobile() ? "98%" : "380px";
        iframe.style.transition = "width 1s, height 1s";
        iframe.style.filter = "drop-shadow(8px 8px 8px rgba(0, 0, 0, 0.4))";
        iframe.setAttribute("class", "animate__animated animate__fadeInRight");
        document.body.appendChild(iframe);
    },

    initKMWebView() {
        let iframe = document.createElement("iframe");
        iframe.setAttribute("src", EcpController.BASE_URL + '/view/webView/webView.html');
        iframe.setAttribute("frameBorder", "0");
        iframe.id = EcpController.KM_COPILOT_WEB_ID;
        iframe.style.height = this._isMobile() ? "98%" : EcpController.MODE == 'ECP' ? "calc(100vh - 0px)" : "calc(100vh - 10px)";
        iframe.style.width = this._isMobile() ? "98%" : "calc(70vh - 0px)";
        if ('teams' == EcpController.NOW_SOURCE) {
            iframe.style.width = "50%";
        }
        iframe.style.zIndex = "999";
        iframe.style.position = "fixed";
        iframe.style.bottom = "0px";
        iframe.style.right = this._isMobile() ? "0vh" : "calc(44vh - 0px)";
        iframe.style.borderRadius = "10px";
        iframe.style.display = "none";
        iframe.style.backdropFilter = "blur(5px)";
        iframe.style.padding = "5px";
        iframe.style.minWidth = this._isMobile() ? "98%" : "600px";
        iframe.style.transition = "rigth 1s, width 1s, height 1s";
        iframe.style.filter = "drop-shadow(8px 8px 8px rgba(0, 0, 0, 0.4))";
        iframe.setAttribute("class", "animate__animated animate__fadeInRight");
        document.body.appendChild(iframe);
    },
    initKMHistory() {
        let iframe = document.createElement("iframe");
        iframe.setAttribute("src", EcpController.BASE_URL + '/view/history/history.html');
        iframe.setAttribute("frameBorder", "0");
        iframe.id = EcpController.KM_COPILOT_HISTORY_ID;
        iframe.style.height = this._isMobile() ? "98%" : EcpController.MODE == 'ECP' ? "calc(100vh - 0px)" : "calc(100vh - 10px)";
        iframe.style.width = this._isMobile() ? "68%" : "calc(25vh - 0px)";
        if ('teams' == EcpController.NOW_SOURCE) {
            iframe.style.width = "50%";
        }
        iframe.style.zIndex = "999";
        iframe.style.position = "fixed";
        iframe.style.bottom = "0px";
        iframe.style.right = this._isMobile() ? "0vh" : "calc(44vh - 0px)";
        iframe.style.borderRadius = "10px";
        iframe.style.display = "none";
        iframe.style.backdropFilter = "blur(5px)";
        iframe.style.padding = "5px";
        iframe.style.minWidth = this._isMobile() ? "68%" : "250px";
        iframe.style.transition = "rigth 1s, width 1s, height 1s";
        iframe.style.filter = "drop-shadow(8px 8px 8px rgba(0, 0, 0, 0.4))";
        iframe.setAttribute("class", "animate__animated animate__fadeInRight");
        document.body.appendChild(iframe);
    },

    doToggle() {
        if (EcpController.SHOW_TOGGLE) {
            $('#' + EcpController.KM_COPILOT_ID).attr('class', 'animate__animated animate__fadeOutRight');
            $('#' + EcpController.KM_COPILOT_ICON_ID).show(250);
        } else {
            $('#' + EcpController.KM_COPILOT_ID).attr('class', 'animate__animated animate__fadeInRight');
            $('#' + EcpController.KM_COPILOT_ID).css("display", "");
            $('#' + EcpController.KM_COPILOT_ICON_ID).hide(250);
        }
        EcpController.SHOW_TOGGLE = !EcpController.SHOW_TOGGLE;
        setTimeout(function () {
            EcpController.ICON_CLICK = false;
            Window.QbiCopilot.doWebViewToggle(true);
            Window.QbiCopilot.doHistoryToggle(true);
        }, 250);
    },

    doHistoryToggle(close) {
        if (EcpController.SHOW_HISTORYTOGGLE || close) {
            $('#' + EcpController.KM_COPILOT_HISTORY_ID).attr('class', 'animate__animated animate__fadeOutRight');
            $('#' + EcpController.KM_COPILOT_HISTORY_ID).css("zIndex", "0");
            setTimeout(() => {
                $('#' + EcpController.KM_COPILOT_HISTORY_ID).css("zIndex", "-999");
            }, 500);
        } else {
            $('#' + EcpController.KM_COPILOT_HISTORY_ID).attr('class', 'animate__animated animate__fadeInRight');
            $('#' + EcpController.KM_COPILOT_HISTORY_ID).css("display", "");
            $('#' + EcpController.KM_COPILOT_HISTORY_ID).css("zIndex", "999");
            if(EcpController.NOW_SIZE !== 'l'){
                Window.QbiCopilot.doWebViewToggle(true);
            }
        }
        EcpController.SHOW_HISTORYTOGGLE = close ? false : !EcpController.SHOW_HISTORYTOGGLE;
    },

    doWebViewToggle(close) {
        if (EcpController.SHOW_WEB_TOGGLE || close) {
            $('#' + EcpController.KM_COPILOT_WEB_ID).attr('class', 'animate__animated animate__fadeOutRight');
            $('#' + EcpController.KM_COPILOT_WEB_ID).css("zIndex", "-999");
        } else {
            $('#' + EcpController.KM_COPILOT_WEB_ID).attr('class', 'animate__animated animate__fadeInRight');
            $('#' + EcpController.KM_COPILOT_WEB_ID).css("display", "");
            $('#' + EcpController.KM_COPILOT_WEB_ID).css("zIndex", "999");
            if(EcpController.NOW_SIZE !== 'l'){
                Window.QbiCopilot.doHistoryToggle(true);
            }
        }
        EcpController.SHOW_WEB_TOGGLE = close ? false : !EcpController.SHOW_WEB_TOGGLE;
    },

    showText(text) {
        if (!EcpController.SHOW_TOGGLE) {
            EcpController.doToggle();
        }
        setTimeout(function () {
            let kmframe = document.getElementById(EcpController.KM_COPILOT_ID).contentWindow;
            let kmChat = kmframe.document.getElementById('mainFrame').contentWindow;
            kmChat.MessageController.doAddMessage(kmChat.MessageController.text(text), kmChat.MessageController.LEFT);
        }, 500);
    },

    doMoveEventInit() {
        let div = document.getElementById(EcpController.KM_COPILOT_ICON_ID);
        DragUtils.dragOn(div);
        div.addEventListener('mouseenter', (event) => {
            EcpController.doIconShake();
        });
        div.addEventListener('mouseleave', (event) => {
            if (document.self) {
                document.removeEventListener("mousemove", document.self.mouseHandler);
                document.removeEventListener("mouseup", document.self.mouseHandler);
                div.style.cursor = "pointer";
                EcpController.doIconShake();
            }
        });
    },

    doIconShake() {
        $('#' + EcpController.KM_COPILOT_ICON_ID).attr('class', '');
        setTimeout(function () {
            let orignal = "animate__animated animate__rubberBand";
            $('#' + EcpController.KM_COPILOT_ICON_ID).attr('class', orignal);
        }, 50);
    },

    setWebViewPosition() {
        let mainFrame = document.getElementById(EcpController.KM_COPILOT_ID).contentWindow;
        let mainViewWidth = document.getElementById(EcpController.KM_COPILOT_ID).clientWidth;
        let web = document.getElementById(EcpController.KM_COPILOT_WEB_ID);
        let history = document.getElementById(EcpController.KM_COPILOT_HISTORY_ID);
        switch (EcpController.NOW_SIZE) {
            case 's':
                web.style.right = this._isMobile() ? "0px" : mainViewWidth + "px";
                break;
            case 'm':
                web.style.right = this._isMobile() ? "0px" : mainViewWidth + "px";
                web.style.width = "48%";
                mainFrame.Main.doChangeSize(EcpController.NOW_SIZE);
                break;
            case 'l':
                web.style.right = "0px";
                if(history.classList.contains('animate__fadeInRight')){
                    web.style.width = "85%";
                }else{
                    web.style.width = "99%";
                }
                if ('teams' == EcpController.NOW_SOURCE) {
                    web.style.width = "60%";
                }
                break;
        }
    },
    setHistoryPosition() {
        let mainFrame = document.getElementById(EcpController.KM_COPILOT_ID).contentWindow;
        let mainViewWidth = document.getElementById(EcpController.KM_COPILOT_ID).clientWidth;
        let history = document.getElementById(EcpController.KM_COPILOT_HISTORY_ID);
        switch (EcpController.NOW_SIZE) {
            case 's':
                history.style.right = this._isMobile() ? "0px" : mainViewWidth + "px";
                break;
            case 'm':
                history.style.right = this._isMobile() ? "0px" : mainViewWidth + "px";
                break;
            case 'l':
                const calculate = mainFrame.Main.calculateIframeSize(document.body.clientWidth);
                document.getElementById(EcpController.KM_COPILOT_ID).style.width = calculate.mainWidth;
                document.getElementById(EcpController.KM_COPILOT_WEB_ID).style.width = calculate.mainWidth;
                document.getElementById(EcpController.KM_COPILOT_HISTORY_ID).style.right = calculate.sideRight;
                break;
        }
    },


    _isMobile() {
        return /Android|webOS|iPhone|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    },
}

var DragUtils = (function () {
    return {
        dragOn(elem) {
            elem.self = this;
            elem.addEventListener("mousedown", this.mouseHandler);
        },
        dragOff(elem) {
            elem.addEventListener("mousdown", this.mouseHandler);
        },
        mouseHandler(e) {
            if (e.type === "mousedown") {
                e.preventDefault();
                document.div = e.target;
                document.offset = { x: e.offsetX, y: e.offsetY };
                document.self = this.self;
                document.addEventListener("mousemove", this.self.mouseHandler);
                document.addEventListener("mouseup", this.self.mouseHandler);
            } else if (e.type === "mousemove") {
                document.div.style.cursor = "move";
                document.div.style.left = e.clientX - document.offset.x + "px";
                document.div.style.top = e.clientY - document.offset.y + "px";
            } else if (e.type === "mouseup") {
                document.removeEventListener("mousemove", document.self.mouseHandler);
                document.removeEventListener("mouseup", document.self.mouseHandler);
                document.div.style.cursor = "pointer";
                EcpController.doIconShake();
            }
        }
    }
})();

window.onresize = function () {
    let img = document.getElementById(EcpController.KM_COPILOT_ICON_ID);
    img.style.bottom = "40px";
    img.style.right = "30px";
    img.style.top = "";
    img.style.left = "";
    EcpController.doIconShake();
    EcpController.setWebViewPosition();
    EcpController.setHistoryPosition();
};

window.addEventListener("load", function (event) {
    EcpController.doLoad();
});

Window.QbiCopilot = EcpController;

// ----------------------------判斷滑鼠點擊----------------------------
// document.addEventListener('mousedown', (e) => {
//     let targetID = e.target.id;
//     if (Window.QbiCopilot.KM_COPILOT_ICON_ID !== targetID) {
//         if (Window.QbiCopilot.SHOW_TOGGLE && !Window.QbiCopilot.pin) {
//             Window.QbiCopilot.doToggle();
//         }
//     }
// });

document.addEventListener("mouseover", (e) => {
    if (EcpController.ICON_CLICK) {
        return;
    }
    if (Window.QbiCopilot.KM_COPILOT_ICON_ID !== e.target.id &&
        Window.QbiCopilot.KM_COPILOT_ID !== e.target.id &&
        Window.QbiCopilot.KM_COPILOT_WEB_ID !== e.target.id &&
        Window.QbiCopilot.KM_COPILOT_HISTORY_ID !== e.target.id) {
        if (Window.QbiCopilot.SHOW_TOGGLE && !Window.QbiCopilot.pin) {
            Window.QbiCopilot.doToggle();
            EcpController.ICON_CLICK = false;
        }
    }
});
