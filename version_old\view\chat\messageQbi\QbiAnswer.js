var QbiAnswer = {
  TEMPLATE: `
    <div class="QbiAnswer_Container">
            {contentArray}
            <div class="MessageText_Satis">{timestamp}
                <div class="satisfactionBar animate__animated animate__fadeInUp">
                    <image id="{messageId}-1" src="../../image/dislike.png" class="satisfactionBtnDislike" title="{satisfactDislikeText}"
                    onclick="ChatEvent.onSatisfactionClick(this,'{messageId}','-1');"></image>
                    <image id="{messageId}0" src="../../image/ok.png" class="satisfactionBtnOk" title="{satisfactOKText}"
                    onclick="ChatEvent.onSatisfactionClick(this,'{messageId}','0');"></image>
                    <image id="{messageId}1" src="../../image/like.png" class="satisfactionBtnLike" title="{satisfactLikeText}"
                    onclick="ChatEvent.onSatisfactionClick(this,'{messageId}','1');"></image>
                    <image src="../../image/copy_1.png" class="satisfactionBtnCopy" title="{satisfactCopyText}"
                    onclick="ChatEvent.onCopyClick(this,'{copyString}');"></image>
                    <img class="SpeakMessageVolume" title="{SpeakMessageVolumeHint}" src="../../image/SingalSpeech_.png" onclick="WebSpeechSynthesis.speak('{speechSynthesisString}')">    
                </div>
            </div>
    </div>
    `,
  MESSAGE_TEMPLATE: `
        <div class="MessageText">
            <font class="Copy-{copyId} WordBreakAll">{content}</font>
        </div>
        `,
  MESSAGE_TEMPLATE_GENERALTEXT:`
    <div style="padding-top: 10px;">
        <div style="display: inline-block;width:100%"><font style="color:rgb(234, 107, 102); font-size: smaller;">{gptAlert}</font></div>
    </div>
  `,
  MESSAGE_TEMPLATE_VIEWNORE: `
        <div class="MessageText">
            <font class="Copy-{copyId} WordBreakAll">{content}</font>
            <div class="ViewMoreContainer">
                <span class="ViewMoreText">
                    <a href="#{messageId}"
                        onclick="ChatEvent.onViewMoreClick(this,'{inputQuestion}','{messageId}');">{viewMore}</a>
                </span>
            </div>
        </div>
    `,
  NO_MESSAGEID_TEMPLATE: `
        {contentArray}
        <div class="MessageText_Satis">{timestamp}
        </div>
    `,

  doInit() {
    ThirdPartyTool.includeCSS("./messageQbi/view/css/QbiAnswerStyle.css");
    ThirdPartyTool.includeJS("./messageQbi/view/QbiText.js");
    ThirdPartyTool.includeJS("./messageQbi/view/QbiImage.js");
    ThirdPartyTool.includeJS("./messageQbi/view/QbiCard.js");
    ThirdPartyTool.includeJS("./messageQbi/view/QbiAudio.js");
    ThirdPartyTool.includeJS("./messageQbi/view/QbiVideo.js");
    ThirdPartyTool.includeJS("./messageQbi/view/QbiFile.js");
    ThirdPartyTool.includeJS("./messageQbi/view/QbiQuickReply.js");
    ThirdPartyTool.includeJS("./messageQbi/view/QbiWebView.js");
    ThirdPartyTool.includeJS("./messageQbi/view/QbiMediaCard.js");
    ThirdPartyTool.includeJS("./messageQbi/view/QbiJson.js");
    ThirdPartyTool.includeJS("./messageQbi/view/QbiExternal.js");
    setTimeout(function () {
      QbiQuickReply.initQuickReplyPool();
      QbiAnswer["Text"] = QbiText.create;
      QbiAnswer["Html"] = QbiText.create;
      QbiAnswer["Image"] = QbiImage.create;
      QbiAnswer["LinkImage"] = QbiImage.create;
      QbiAnswer["Cards"] = QbiCard.create;
      QbiAnswer["List"] = QbiCard.create;
      QbiAnswer["Audio"] = QbiAudio.create;
      QbiAnswer["Video"] = QbiVideo.create;
      QbiAnswer["File"] = QbiFile.create;
      QbiAnswer["QuickReply"] = QbiQuickReply.create;
      QbiAnswer["WebView"] = QbiWebView.create;
      QbiAnswer["MediaCard"] = QbiMediaCard.create;
      QbiAnswer["Json"] = QbiJson.create;
      QbiAnswer["External"] = QbiExternal.create;
      console.log("[QbiAnswer doInit] init success.");
    }, 1000);
  },

  doDisplayAnswer(messageId, content , generalText) {
    if(typeof content === "string") content = JSON.parse(content);
    let answer = content;
    let timestamp = Chat.Chat_HistoryTimestamp || CommonUtil.formatStringDate(new Date()); 
    let answerType = answer.type.charAt(0).toUpperCase() + answer.type.slice(1);
    let html =
      messageId == null ? QbiAnswer.NO_MESSAGEID_TEMPLATE : QbiAnswer.TEMPLATE;
    let contentArray = "";
    if (answerType == "Multiple") {
      answer.ans.forEach((contentItem, index) => {
        const isLast = index === answer.ans.length - 1;
        contentArray += QbiAnswer.addMessage(contentItem.type,contentItem,isLast,messageId,generalText);
        if (!isLast)contentArray += "</br></br>"; 
      });
    } else {
      contentArray = QbiAnswer.addMessage(answerType, answer, true, messageId,generalText);
    }

    // if (answerType == "Multiple") {
    //   html = html.replaceAll("QbiAnswer_Container", "Multiple_Container");
    // }
    html = html.replaceAll("{contentArray}", contentArray);
    html = html.replaceAll("{messageId}", messageId);
    let copyId = CommonUtil.getRandomUuid();
    html = html.replaceAll("{copyString}", copyId);
    html = html.replaceAll("{copyId}", copyId);
    html = html.replace("{satisfactLikeText}", Text["satisfactLikeText"]);
    html = html.replace("{satisfactOKText}", Text["satisfactOKText"]);
    html = html.replace("{satisfactDislikeText}", Text["satisfactDislikeText"]);
    html = html.replace("{satisfactCopyText}", Text["satisfactCopyText"]);
    html = html.replaceAll("{SpeakMessageVolumeHint}",Text["SpeakMessageVolumeHint"]);
    html = html.replaceAll("{timestamp}", timestamp);
    html = html.replaceAll("{speechSynthesisString}",CommonUtil.stripHTML(MessageController.TotalMessage_Synthesis,true));
    html = html.replaceAll("{viewMore}", Text["viewMore"]);
    html = html.replaceAll("{inputQuestion}",Chat.Chat_inputQuestion.replace(/'/g, "\\'"));

    if (CodeParse.isContainCode(html)) {
      html = CodeParse.getContentParse(html);
    }
    QbiAnswer.handleAIMessageInfo(answerType, answer, messageId);
    MessageController.doAddMessage(html, MessageController.LEFT);
    Chat.PageApp.LOCK_SEND = false;
    
  },

  addMessage(answerType, content, isLast, messageId,generalText) {
    //判斷是否為多答數組最後一位以處理(QuickReply及WebView會出現在多答數組的最後位置)
    if (answerType === "QuickReply" && content.hasOwnProperty("QuickReply")) {
      //處理QuickReply
      if (content.QuickReply.hasOwnProperty("quick_reply_items")) {
        QbiAnswer["QuickReply"](content.QuickReply);
      }
      //處理WebView
      if (content.QuickReply.hasOwnProperty("WebViewUrl")) {
        QbiAnswer["WebView"](content.QuickReply.WebViewUrl);
      }
      //處理答案格式
      if (content.QuickReply.hasOwnProperty("type")) {
        return QbiAnswer.doFilterQbiMessage(
          QbiAnswer[content.QuickReply.type](content.QuickReply),
          isLast,
          messageId,
          content.QuickReply.type,
          generalText
        );
      }
    } else {
      //處理WebView
      if (content.hasOwnProperty("WebViewUrl")) {
        QbiAnswer["WebView"](content.WebViewUrl);
      }
      //處理答案格式
      if (content.hasOwnProperty("type")) {
        return QbiAnswer.doFilterQbiMessage(
          QbiAnswer[answerType](content),
          isLast,
          messageId,
          answerType,
          generalText
        );
      }
    }
  },

  doFilterQbiMessage(ShowContent, isLast, messageId, answerType ,generalText) {
    let copyContent = "";
    let data = CommonUtil.getLocalStorage(MessageController.StorageTOPN_KEY)[messageId];
    let TEMPLATE =
      isLast && !(data == null)
        ? this.MESSAGE_TEMPLATE_VIEWNORE
        : this.MESSAGE_TEMPLATE;
    if(generalText&&generalText!=="EXECUTE_EXPLORE_MAP"&&answerType==="Text"){
      const closingDivIndex = TEMPLATE.indexOf('</div>');
      let genGptText = "";
      switch(generalText){
        case "EXECUTE_PROMPT":
          genGptText = Chat.PageApp.ServerConfig?.aIPromptGenerateText==null?Text['gptAlert']:Chat.PageApp.ServerConfig.aIPromptGenerateText;
          break;
        default:
          genGptText = Chat.PageApp.ServerConfig?.functionGenerateText==null?Text['gptAlert']:Chat.PageApp.ServerConfig.functionGenerateText;
          break;
      }
      let templateTemp = this.MESSAGE_TEMPLATE_GENERALTEXT.replace('{gptAlert}',genGptText===""?generalText:genGptText);
      TEMPLATE = TEMPLATE.slice(0, closingDivIndex) + templateTemp + TEMPLATE.slice(closingDivIndex);
    }
    TEMPLATE = TEMPLATE.replaceAll("{content}", ShowContent);
    
    //修正套件跑版問題
    if (answerType === "Cards" || answerType === "MediaCard") {
      TEMPLATE = TEMPLATE.replaceAll("MessageText", "MessageText MessageCard");
    }
    QbiAnswer.saveTotalMessage_Synthesis(answerType, ShowContent);

    return TEMPLATE;
  },

  saveTotalMessage_Synthesis(answerType, ShowContent) {
    let Message_Synthesis = ""
    switch (answerType) {
      case "Image":
        Message_Synthesis += Text["image"]
        break;
      case "LinkImage":
        Message_Synthesis += Text["image"]
        break;
      case "Audio":
        Message_Synthesis += Text["audio"]
        break;
      case "Video":
        Message_Synthesis += Text["video"]
        break;
      case "File":
        Message_Synthesis += Text["file"]
        break;
      case "MediaCard":
        if (ShowContent.includes("影片連結")) Message_Synthesis += Text["video"];
        if (ShowContent.includes("圖片連結")) Message_Synthesis += Text["image"];
        break;
    }
    Message_Synthesis += CommonUtil.stripHTML(ShowContent, true);
    MessageController.TotalMessage_Synthesis += Message_Synthesis;
  },
  handleAIMessageInfo(answerType, answer, messageId) {
    //儲存對話紀錄
    if (answerType === "List") {
      answerType = "Cards";
    }
    Chat.Chat_answer = JSON.stringify(answer);
    Chat.Chat_messageId = messageId == null ? "" : messageId;

    ActionFunction.HISTORY_RECORD.messages[1].type = "ai";
    ActionFunction.HISTORY_RECORD.messages[1].message =
      MessageController.TotalMessage_Synthesis;
    if (Chat.Chat_dataSource == "function" && answerType == "Text") {
      Chat.Chat_answerType = "gptText";
    } else if (Chat.Chat_dataSource === "web") {
      Chat.Chat_dataSource = "function";
      Chat.Chat_answerType = answerType;
    } else if (Chat.Chat_dataSource == "qa" && answerType == "External") {
      Chat.Chat_answer = JSON.stringify({ text: Text['notSupportformat'], type: "text" });
      Chat.Chat_answerType = "Text";
    } else {
      Chat.Chat_answerType = answerType;
    }
  },
};

var CodeParse = {
  isContainCode(content) {
    let regex = /```([\s\S]*?)```/g;
    return content.match(regex) != null;
  },

  getContentParse(content) {
    let regex = /```([\s\S]*?)```/g;
    let replacedString = content.replace(regex, "<pre><code>$1</code></pre>");
    return replacedString;
  },
};
