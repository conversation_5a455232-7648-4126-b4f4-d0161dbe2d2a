// 一般Copilot回應模式
var ActionCopilot = {

    ANSWER_GPT: "0",
    ANSWER_KM: "1",
    ANSWER_GPT_AND_KM: "2",
    ANSWER_GPT_PLUS_EASY: "3",

    KEY_LAST_ARGS: 'KEY_LAST_ARGS',
    timer: "",
    execute(args) {
        HttpQuery.doRequest('/openapi/copilot/getSummary', args, function (result) {
            try {
                // 錯誤回應
                if (result.hasOwnProperty('errorType') || result?.httpCode===429|| result?.httpCode===400) {
                    ActionManager.handleErrorState(result);
                    return;
                }
                
                let summary = result.answer;
                let content = Text['searching'] + summary;
                args.summary = summary;
                ActionManager.isDoAddHistory = false;
                if (summary.length > 0) {
                    MessageController.doAddMessage(MessageController.notice(content), MessageController.LEFT);
                    ActionFunction.HISTORY_RECORD_HUMAN.message = summary;
                    ActionFunction.HISTORY_RECORD.messages[0] = ActionFunction.HISTORY_RECORD_HUMAN
                }else{
                    ActionFunction.HISTORY_RECORD_HUMAN.message = Chat.Chat_inputQuestion;
                    ActionFunction.HISTORY_RECORD.messages[0] = ActionFunction.HISTORY_RECORD_HUMAN
                }

                if (Chat.PageApp.NowCatalog.FId !== 'all') {
                    args.knowledgeCatalogID = Chat.PageApp.NowCatalog.FId;
                }
                args.responseType = "KM";
                Chat.Chat_dataSource = "km";
            } catch (e) {
                ChatEvent._showError();
                return;
            }
            ActionCopilot.doFlowControl(args, true)
        });
    },

    doFlowControl(args, isNeedShowSearch) {
        try {
            MessageController.TotalMessage_Synthesis = "";
            if (isNeedShowSearch) {
                ActionManager.showWaitingMessage();
            }
            if (args.inputQuestion.startsWith("@") || args.inputQuestion.startsWith("＠")) {
                args.topN = [];
                ActionCopilot.doGetAnswerResponse(args);
            } else {
                //強制外部搜尋判斷
                if (!Chat.PageApp.isKnowledgeFirst && ((Chat.PageApp.ServerConfig.enableWebSearch && UserInfo.getData().mode == "employee") || Config.FORCE_WEB_SEARCH_ENABLE)) {
                    MessageController.doParseGoogleSearchMessage(args, null);
                } else {
                    //關鍵字判斷
                    if (Chat.PageApp.QbiCopilotInfo.checkKeyWord) {
                        if (parent.parent.Jui && !parent.parent.EcpController.qsEnableCopilot && Chat.PageApp.QbiCopilotInfo.checkKeyWord) {
                            Chat.PageApp.roomId = parent.parent.window.ChatEcp.getCurrentRoomId();
                        }
                        ActionCopilot.checkKeyWord(args).then((isKeyWord) => {
                            ActionCopilot.doGetTopN(args).then((result) => {
                                if (isKeyWord) {
                                    ActionCopilot.doKeyWordResponse(args, result);
                                } else {
                                    ActionCopilot.doGeneralResponse(args, result);
                                }

                            });
                        });
                    } else {
                        ActionCopilot.doGetTopN(args).then((result) => {
                            ActionCopilot.doGeneralResponse(args, result);
                        });
                    }
                }
            }
        } catch {
            console.warn('[ActionCopilot - doFlowControl] format wrong..');
            Chat.PageApp.LOCK_SEND = false;
        }
    },

    doGeneralResponse(args, result) {
        try {
            //多意圖判斷顯示
            ActionManager.doShowIntentsQuestion(result);
            args.topN = result.topN;
            CommonUtil.setLocalStorage(ActionCopilot.KEY_LAST_ARGS, JSON.stringify(args));
            if (args.topN.length == 0) {
                if ((Chat.PageApp.ServerConfig.enableWebSearch && UserInfo.getData().mode == "employee") || Config.FORCE_WEB_SEARCH_ENABLE) {
                    MessageController.doParseGoogleSearchMessage(args, null);
                } else {
                    let content = MessageController.text(Chat.PageApp.ServerConfig?.noAnswerReply==null?Text['chatNotFound']: Chat.PageApp.ServerConfig?.noAnswerReply);
                    Chat.Chat_dataSource = "noAnswer";
                    Chat.Chat_answer = JSON.stringify({ text: Chat.PageApp.ServerConfig?.noAnswerReply==null?Text['chatNotFound']: Chat.PageApp.ServerConfig?.noAnswerReply, type: "text" });
                    Chat.Chat_answerType = "Text";
                    MessageController.doAddMessage(content, MessageController.LEFT);
                    Chat.PageApp.LOCK_SEND = false;
                }
                if (Chat.PageApp.QbiCopilotInfo.isTTSEnable && Chat.isAutoSpeechSynthesis) {
                    WebSpeechSynthesis.speak(CommonUtil.stripHTML(MessageController.TotalMessage_Synthesis,true));
                }
            } else {
                ActionCopilot.doGetAnswerResponse(args);
            }
        } catch (e) {
            CommonUtil.deleteLocalStorage(ActionCopilot.KEY_LAST_ARGS);
            ChatEvent._showError();
            Chat.PageApp.LOCK_SEND = false;
            return;
        }
    },
    async doGetAnswerResponse(args) {
        try {
            const result = await HttpQuery.doRequestFetch('/openapi/copilot/getAnswerResponse', args);
            if (result && result.__stream) {
                // streaming 已經處理完，不用再往下走
                return;
            }
            if(result?.httpCode===429||result?.httpCode===400){
                ActionManager.handleErrorState(result);
                return;
            }
            result.topN = args.topN;
            Chat.Chat_answer = result;
            ActionFunction.HISTORY_RECORD_AI.message = 
            result?.answerType === "text" 
            ? result?.answer 
            : result?.answer?.answers.map(item => item.answer).join('');
            let answerMode = Chat.PageApp.ServerConfig.answerMode;
            Chat.Chat_inputQuestion = args.inputQuestion;
            ChatEvent.doStorageTOPN_KEY(args,result.messageId);
            
            if (ActionCopilot.ANSWER_GPT === answerMode || ActionCopilot.ANSWER_GPT_AND_KM === answerMode) {
                MessageController.doParseGPTMessage(result, args.topN, args.inputQuestion,args);
            }
            if (ActionCopilot.ANSWER_KM === answerMode) {
                MessageController.doParseKMMesaage(result, args.topN, args);
            }
            if (ActionCopilot.ANSWER_GPT_PLUS_EASY === answerMode) {
                MessageController.doParseGPT_EasyMessage(result, args.topN, args.inputQuestion);
            }
            if (Chat.PageApp.QbiCopilotInfo.isTTSEnable && Chat.isAutoSpeechSynthesis) {
                WebSpeechSynthesis.speak(CommonUtil.stripHTML(MessageController.TotalMessage_Synthesis,true));
            }
        } catch (e) {
            console.warn('[ActionCopilot - doGetAnswerResponse] format wrong..');
            Chat.PageApp.LOCK_SEND = false;
        }
    },
    // doGetAnswerResponse(args) {
    //     try {
    //         HttpQuery.doRequest('/openapi/copilot/getAnswerResponse', args, function (result) {
    //             if(result?.httpCode===429||result?.httpCode===400){
    //                 ActionManager.handleErrorState(result);
    //                 return;
    //             }
    //             result.topN = args.topN;
    //             Chat.Chat_answer = result;
    //             ActionFunction.HISTORY_RECORD_AI.message = 
    //             result?.answerType === "text" 
    //             ? result?.answer 
    //             : result?.answer?.answers.map(item => item.answer).join('');
    //             let answerMode = Chat.PageApp.ServerConfig.answerMode;
    //             Chat.Chat_inputQuestion = args.inputQuestion;
    //             ChatEvent.doStorageTOPN_KEY(args,result.messageId);
                
    //             if (ActionCopilot.ANSWER_GPT === answerMode || ActionCopilot.ANSWER_GPT_AND_KM === answerMode) {
    //                 MessageController.doParseGPTMessage(result, args.topN, args.inputQuestion,args);
    //             }
    //             if (ActionCopilot.ANSWER_KM === answerMode) {
    //                 MessageController.doParseKMMesaage(result, args.topN, args);
    //             }
    //             if (ActionCopilot.ANSWER_GPT_PLUS_EASY === answerMode) {
    //                 MessageController.doParseGPT_EasyMessage(result, args.topN, args.inputQuestion);
    //             }
    //             if (Chat.PageApp.QbiCopilotInfo.isTTSEnable && Chat.isAutoSpeechSynthesis) {
    //                 WebSpeechSynthesis.speak(MessageController.TotalMessage_Synthesis);
    //             }
    //         });
    //     } catch {
    //         console.warn('[ActionCopilot - doGetAnswerResponse] format wrong..');
    //         Chat.PageApp.LOCK_SEND = false;
    //     }
    // },

    doGetTopN(args) {
        try {
            return new Promise((resolve, reject) => {
                HttpQuery.doRequest('/openapi/copilot/getTopN', args, function (result) {
                    try {
                        resolve(result)
                    } catch (e) {
                        CommonUtil.deleteLocalStorage(ActionCopilot.KEY_LAST_ARGS);
                        ChatEvent._showError();
                        return;
                    }
                });
            });
        } catch {
            console.warn('[ActionCopilot - doGetTopN] format wrong..');
            Chat.PageApp.LOCK_SEND = false;
        }
    },
    doKeyWordResponse(args, result) {
        try {
            return new Promise((resolve, reject) => {
                try {
                    if (parent.parent.Jui && !parent.parent.EcpController.qsEnableCopilot) {
                        if (Chat.PageApp.roomId) {
                            parent.parent.window.ChatEcp.keyWordToKnowlege({ roomId: Chat.PageApp.roomId, keyWord: args.inputQuestion });
                        }
                        Chat.PageApp.roomId = null;
                    }
                    HttpQuery.doRequest('/openapi/copilot/generateQuestion', {
                        inputQuestion: args.inputQuestion,
                        login: args.login,
                        chatId: args.chatId,
                        topN: result.topN
                    }, function (keyWordresult) {
                        if (keyWordresult.questions && keyWordresult.questions.length !== 0) {
                            //儲存對話紀錄        
                            Chat.Chat_answer = keyWordresult;
                            Chat.Chat_answerType = "Cards";
                            Chat.Chat_dataSource = "llm";
                            if (parent.parent.Jui && !parent.parent.EcpController.qsEnableCopilot) {
                                MessageController.doDisaplayKeyWordCard(keyWordresult.questions);
                            } else {
                                MessageController.doDisaplayKeyWordCard(keyWordresult.questions);
                            }
                        }
                        else {
                            let answer = Text['keyWordNotFonud_front'] + args.inputQuestion + Text['keyWordNotFonud_end'];
                            Chat.Chat_answer = JSON.stringify({ text: answer, type: "text" });
                            Chat.Chat_answerType = "text";
                            Chat.Chat_dataSource = "noAnswer";
                            MessageController.doAddMessage(MessageController.text(answer), MessageController.LEFT);
                            Chat.PageApp.LOCK_SEND = false;
                        }
                    });
                } catch (e) {
                    let content = MessageController.text( Chat.PageApp.ServerConfig?.noAnswerReply==null?Text['referenceError']: Chat.PageApp.ServerConfig?.noAnswerReply);
                    Chat.Chat_answerType = "text";
                    Chat.Chat_dataSource = "noAnswer";
                    MessageController.doAddMessage(content, MessageController.LEFT);
                    Chat.PageApp.LOCK_SEND = false;
                    console.warn('[ActionCopilot - generateQuestion] format wrong..');
                }
            })
        } catch {
            console.warn('[ActionCopilot - doKeyWordResponse] format wrong..');
            Chat.PageApp.LOCK_SEND = false;
        }
    },


    doShowCards(args, messageId, isQA) {
        try {
            let chunkSourceIds = []
            if (messageId != null) {
                chunkSourceIds = Object.keys(args.answerSource);
            }

            // 針對來源重新對topN排序
            if (messageId != null) {
                let rankTopN = [];
                for (let i = 0; i < chunkSourceIds.length; i++) {
                    rankTopN.push(args.answerSource[chunkSourceIds[i]]);
                }
                args.topN = rankTopN;
            }

            // 拿來源知識列表第一個當作top1出答案     
            let chunkList = [];
            if (messageId != null) {
                for (let i = 0; i < chunkSourceIds.length; i++) {
                    let kmId = chunkSourceIds[i];
                    chunkList.push(args.answerSource[kmId]);
                }
            } else {
                chunkList = args.topN;
            }

            if (messageId != null) {
                if (isQA) {
                    let top1AnswerKMId = chunkList.shift().metadata.sourceId;
                    ActionQbiBotPlus.doDisaplyAnswer(messageId, top1AnswerKMId, false, function (ret) {
                        ActionQbiBotPlus.doDisaplayAnswerCard(chunkList);
                    });
                } else {
                    // top1 + 建議問
                    let top1AnswerKMId = chunkList.shift().metadata.knowledgeId;
                    let top1Chunk = args.answerSource[top1AnswerKMId];
                    ActionCopilot.doDisaplyAnswer(messageId, top1AnswerKMId, top1Chunk, function (ret) {
                        // 其餘答案組成卡片
                        ActionCopilot.doShowReference(messageId, chunkList);
                    });
                }
            } else {
                // 參考問
                ActionCopilot.doShowReference(messageId, chunkList);
            }
        } catch (e) {
            let content = MessageController.text(Chat.PageApp.ServerConfig?.noAnswerReply==null?Text['referenceError']: Chat.PageApp.ServerConfig?.noAnswerReply);
            Chat.Chat_answerType = "text";
            Chat.Chat_dataSource = "noAnswer";
            MessageController.doAddMessage(content, MessageController.LEFT);
            console.warn('[ActionCopilot - doShowCards] format wrong..');
        } finally {
            setTimeout(function () {
                parent.Main.showLoading(false, 250);
            }, 400);
            Chat.PageApp.LOCK_SEND = false;
        }
    },

    doShowReference(messageId, chunkList) {
        let knowIds = [];
        let FQACardAnswer = [];
        let maxRefer = parseInt(Chat.PageApp.ServerConfig.referMax);
        for (let i = 0; i < chunkList.length; i++) {
            let kmId = chunkList[i].metadata.knowledgeId;
            let sourceName = chunkList[i].metadata.sourceName;
            if (!knowIds.includes(kmId)) {
                knowIds.push(kmId);
                let code = { 'sourceName': sourceName, 'chunk': chunkList[i], 'messageId': messageId };
                CommonUtil.saveData(Chat.DATA_KM, kmId, code);
                let item = {
                    "FDisplayText": sourceName,
                    "ValueContent": "",
                    "FCode": kmId,
                    "Option": Chat.DATA_KM,
                    "FName": sourceName,
                    "FShowText": sourceName
                };
                if (knowIds.length <= maxRefer) {
                    FQACardAnswer.push(item);
                }
            }
        }
        if (FQACardAnswer.length > 0) {
            let cardAnswer = {
                "FQACardColumn": [
                    {
                        "FQACardAnswer": FQACardAnswer,
                        "thumbnailImageUrl": "",
                        "FMsgAnswer": messageId == null ? Chat.PageApp.ServerConfig.referDesc : Chat.PageApp.ServerConfig.releatedDesc,
                        "type": "Cards",
                        "title": messageId == null ? Chat.PageApp.ServerConfig.referTitle : Chat.PageApp.ServerConfig.releatedTitle,
                    }
                ],
                "type": "Cards",
            };
            Chat.Chat_kmIds = knowIds;
            QbiAnswer.doDisplayAnswer(null, JSON.stringify(cardAnswer),null);
        } else {
            if (messageId == null) {
                let content = MessageController.text(Chat.PageApp.ServerConfig?.noAnswerReply==null?Text['referenceError']: Chat.PageApp.ServerConfig?.noAnswerReply);
                Chat.Chat_answerType = "text";
                Chat.Chat_dataSource = "noAnswer";
                MessageController.doAddMessage(content, MessageController.LEFT);
            }
        }
    },

    doDisaplyAnswer(messageId, kmId, chunk, callback, showEmpty) {
        let answerMode = Chat.PageApp.ServerConfig.answerMode;
        MessageController.TotalMessage_Synthesis = "";
        HttpQuery.doRequest('/openapi/copilot/getKMContent', { sourceId: kmId }, function (result) {
            let html = result.content;
            let contentHTML = CommonUtil.stripHTML(html, true);
            if (contentHTML.length > 0) {
                let template = '';
                if (chunk != null) {
                    template = `
                            <br>
                            <font style="color:rgb(0, 88, 176)">{dataSourceText}</font>
                            <br>
                            <a href="#{id}" onclick="ChatEvent.onSourceClick('{id}','{source}','km')">{content}</a>
                        `;
                    template = template.replaceAll('{id}', kmId);
                    template = template.replaceAll('{dataSourceText}', Text['dataSourceText']);
                    template = template.replaceAll('{source}', chunk.metadata.sourceName);
                    if ('file' === chunk.metadata.type || 'document' === chunk.metadata.type) {
                        let templateDownload = `<a href="#{id}" onclick="ChatEvent.onSourceClick('{id}','{content}','{type}')"><img src="../../image/file.png" width="15px"></a>`;
                        templateDownload = templateDownload.replaceAll('{id}', chunk.metadata.sourceId);
                        templateDownload = templateDownload.replaceAll('{type}', chunk.metadata.type);
                        template = template.replaceAll('{content}', chunk.metadata.sourceName + templateDownload);
                    } else {
                        template = template.replaceAll('{content}', chunk.metadata.sourceName);
                    }
                }
                let content = {
                    needSatisfaction: messageId != null ? true : false,
                    needSpeechSynthesis: true,
                    content: html,
                    source: template,
                    messageId: messageId,
                    disableLine: true
                }
                Chat.Chat_answer = content;
                if(!(Chat.PageApp.ServerConfig.greeting.FInsideKMId == kmId || Chat.PageApp.ServerConfig.greeting.FOutterKMId == kmId)){
                    ActionManager.isDoAddHistory = true;
                }
                Chat.Chat_answerType = "Text";
                Chat.Chat_dataSource = "km";
                MessageController.doAddMessage(MessageController.text(content), MessageController.LEFT);
            } else {
                if (ActionCopilot.ANSWER_KM === answerMode || showEmpty) {
                    Chat.Chat_answer = JSON.stringify({ text: Text['answerEmpty'], type: "text" });
                    Chat.Chat_answerType = "Text";
                    Chat.Chat_dataSource = "noAnswer";
                    MessageController.doAddMessage(MessageController.text(Text['answerEmpty']), MessageController.LEFT);
                }
            }
            if (callback !== undefined) {
                callback(result);
            }
            if (Chat.PageApp.QbiCopilotInfo.isTTSEnable && Chat.isAutoSpeechSynthesis) {
                WebSpeechSynthesis.speak(CommonUtil.stripHTML(MessageController.TotalMessage_Synthesis,true));
            }
        });
    },
    checkKeyWord(args) {
        try {
            return new Promise((resolve, reject) => {
                HttpQuery.doRequest('/openapi/copilot/checkKeyWord', args, function (result) {
                    resolve(result.checkKeyWord);
                });
            });
        } catch {
            console.warn('[ActionCopilot - checkKeyWord] format wrong..');
            Chat.PageApp.LOCK_SEND = false;
        }
    },

}