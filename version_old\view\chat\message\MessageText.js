var MessageText = {

    lengthCheck: 16,
    template_Start: `
    <div class="MessageText_Container">
    `,

    template: `
    <div class="MessageText">
        <font class="Copy-{copyId} WordBreakAll" >{content}</font>
        {source}
    `,

    template_ViewMore: `
    <div class="ViewMoreContainer">
    <span class="ViewMoreText">
        <a href="#{messageId}"
            onclick="ChatEvent.onViewMoreClick(this,'{inputQuestion}','{messageId}');">{viewMore}</a>
    </span>
    </div>
    `,

    template_SatisAndSpeech: `
    <div class="MessageText_Satis">
    {timestamp}
    <div class="satisfactionBar animate__animated animate__fadeInUp">
        <image id="{messageId}-1" src="../../image/dislike.png" class="satisfactionBtnDislike" title="{satisfactDislikeText}"
        onclick="ChatEvent.onSatisfactionClick(this,'{messageId}','-1');"></image>
        <image id="{messageId}0" src="../../image/ok.png" class="satisfactionBtnOk" title="{satisfactOKText}"
        onclick="ChatEvent.onSatisfactionClick(this,'{messageId}','0');"></image>
        <image id="{messageId}1" src="../../image/like.png" class="satisfactionBtnLike" title="{satisfactLikeText}"
        onclick="ChatEvent.onSatisfactionClick(this,'{messageId}','1');"></image>
        <image src="../../image/copy_1.png" class="satisfactionBtnCopy" title="{satisfactCopyText}"
        onclick="ChatEvent.onCopyClick(this,'{copyString}');"></image>
        <img class="SpeakMessageVolume"  title="{SpeakMessageVolumeHint}"  src="../../image/SingalSpeech_.png" onclick="WebSpeechSynthesis.speak('{speechSynthesisString}')">
    </div>
    <div>
    `,

    template_TimesTampAndSpeech: `
    <div class="MessageText_Satis">
    {timestamp}
    <div class="satisfactionBar animate__animated animate__fadeInUp">
        <img class="SpeakMessageVolume"  title="{SpeakMessageVolumeHint}"  src="../../image/SingalSpeech_.png" onclick="WebSpeechSynthesis.speak('{speechSynthesisString}')">
    </div>
    </div>
    `,
    template_timestamp: `
    <div class="MessageText_Satis">
        {timestamp}
    </div>
    `,
    template_End: `
    </div>
    `,
    
    template_gptAlert: `
    <div style="display: inline-block;width:100%"><font style="color:rgb(234, 107, 102); font-size: smaller;">
    {gptAlert}
    </font></div>
    `,

    create(args) {
        let timestamp = Chat.Chat_HistoryTimestamp || CommonUtil.formatStringDate(new Date()); 
        if (typeof args == 'object') {
            let data = CommonUtil.getLocalStorage(MessageController.StorageTOPN_KEY)[args.messageId];
            let html =
                MessageText.template_Start +
                MessageText.template +
                (data == null ? "" : MessageText.template_ViewMore) + "</div>" +
                (args.needSatisfaction === true ? MessageText.template_SatisAndSpeech : MessageText.template_TimesTampAndSpeech) +
                MessageText.template_End
                ;
            // let html = args.needSatisfaction ? MessageText.template_SatisAndSpeech : MessageText.template_TimesTampAndSpeech;
            // html += Chat.PageApp.QbiCopilotInfo.isTTSEnable ? args.needSpeechSynthesis ?
            //     MessageText.template_speechSynthesis :
            //         args.needSatisfaction ? MessageText.template_speechSynthesis
            //             : MessageText.template_timestamp : MessageText.template_timestamp;

            let content = args.content;
            if (!args.disableLine) {
                args.content = args.content.replaceAll('\\n', '<br>');
                args.content = args.content.replaceAll('\n\n', '\n');
                args.content = args.content.replaceAll('\n', '<br>');
            }
            let copyId = CommonUtil.getRandomUuid();
            html = html.replaceAll('{copyString}', copyId);
            html = html.replaceAll('{copyId}', copyId);

            let copyContent = CommonUtil.stripHTML(args.content, true);
            html = html.replaceAll('{speechSynthesisId}', copyId);
            html = html.replaceAll('{speechSynthesisString}', MessageController.escapeHTML(copyContent));
            html = html.replaceAll('{SpeakMessageVolumeHint}', Text['SpeakMessageVolumeHint']);

            MessageController.TotalMessage_Synthesis += copyContent;
            html = html.replace('{satisfactLikeText}', Text['satisfactLikeText']);
            html = html.replace('{satisfactOKText}', Text['satisfactOKText']);
            html = html.replace('{satisfactDislikeText}', Text['satisfactDislikeText']);
            html = html.replace('{satisfactCopyText}', Text['satisfactCopyText']);
            html = html.replaceAll('{timestamp}', timestamp);

            if (CodeParse.isContainCode(content)) {
                html = html.replaceAll('{content}', CodeParse.getContentParse(content));
            } else {
                html = html.replaceAll('{content}', args.content);
            }
            html = MessageText._addSource(args, html);
            html = html.replaceAll('{messageId}', args.messageId);
            html = html.replaceAll('{viewMore}', Text['viewMore']);
            html = html.replaceAll('{inputQuestion}', Chat.Chat_inputQuestion.replace(/'/g, "\\'"));

            return MessageText._setContentStyle(content, html);;
        } else {
            //判斷(提問訊息 && 非文字、語音) 之訊息發送 
            const isSendMessage = CommonUtil.stripHTML(Chat.Chat_inputQuestion, true) == CommonUtil.stripHTML(args, true);
            let html =
                (isSendMessage ? MessageText.template_Start.replace("MessageText_Container","") : MessageText.template_Start) +
                MessageText.template + "</div>" +
                (isSendMessage ? MessageText.template_timestamp : MessageText.template_TimesTampAndSpeech) +
                MessageText.template_End;

            //判斷TTS功能 、 isSendMessage 決定是否顯示播音按鈕
            let orignal = args;
            if (CodeParse.isContainCode(orignal)) {
                args = CodeParse.getContentParse(orignal);
            } else {
                args = args.replaceAll('\\n', '<br>');
                args = args.replaceAll('\n\n', '\n');
                args = args.replaceAll('\n', '<br>');
            }
            //判斷是否為通路顯示文字
            if(Chat.Chat_ChanegeShowQuestion && isSendMessage){
                html = html.replace('{content}', Chat.Chat_showQuestion);
            }else{
                html = html.replace('{content}', args);
            }
            if (!isSendMessage) {
                if(args.includes("{functionGenerateText}")){
                    MessageController.TotalMessage_Synthesis += CommonUtil.stripHTML(args.replaceAll("{functionGenerateText}",""),true);
                }else{
                    MessageController.TotalMessage_Synthesis += CommonUtil.stripHTML(args, true);
                }
            }
            
            html = html.replaceAll('{timestamp}', timestamp);
            if(args.includes("{functionGenerateText}")){
                html = html.replaceAll('{functionGenerateText}',MessageText.template_gptAlert)
                html = html.replaceAll('{gptAlert}', Chat.PageApp.ServerConfig?.functionGenerateText==null?Text['gptAlert']:Chat.PageApp.ServerConfig.functionGenerateText);
            }
            html = html.replaceAll('{speechSynthesisString}', CommonUtil.stripHTML(MessageController.TotalMessage_Synthesis,true));
            html = MessageText._addSource(args, html);

            return MessageText._setContentStyle(args, html);;
        }
    },

    _addSource(args, html) {
        // 來源
        if (args.hasOwnProperty('source')) {
            html = html.replaceAll('{source}', args.source);
        } else {
            html = html.replaceAll('{source}', '');
        }
        return html;
    },

    _setContentStyle(content, html) {
        if (content.length >= MessageText.lengthCheck) {
            html = html.replaceAll('{contentStyle}', 'style="width:100%"');
        } else {
            html = html.replaceAll('{contentStyle}', '');
        }
        return html;
    }
}


var CodeParse = {

    isContainCode(content) {
        let regex = /```([\s\S]*?)```/g;
        return content.match(regex) != null;
    },

    getContentParse(content) {
        let regex = /```([\s\S]*?)```/g;
        let replacedString = content.replace(regex, "<pre><code>$1</code></pre>");
        return replacedString;
    }
}