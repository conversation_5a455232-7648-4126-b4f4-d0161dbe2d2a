/* body{
	background:none;
	background-color: rgb(235 235 235);
} */

#KMCopilotMainView {
    height: calc(100vh) !important;
}

/* --------------視窗按鈕-------------- */
.webviewbackIcon{
    content: url("../image/custom-style-01/back_custom.png") !important;
}
.mainbackIcon{
    content: url("../image/custom-style-01/previous_custom.png") !important;
    /* display: none; */
}
.closeIcon{
    content: url("../image/custom-style-01/cancel_custom.png") !important;
}
.mainhistorytoggleIcon {
    content: url("../image/custom-style-01/cancel_custom.png") !important;
}

/* --------------navBar-------------- */
.navBar {
    background-color: rgb(235 235 235) !important;
}
.title {
    color: rgb(0, 0, 0) !important;
}

/* --------------字體大小按鈕-------------- */
.mainFontSizeIcon{
    content: url("../image/custom-style-01/font_size_custom.png") !important;
}

/* --------------使用者資訊-------------- */
.btn-info {
    background-color: rgb(0 0 0) !important;
    border-color: rgb(0 0 0) !important;
}
.btn-info:hover {
    background-color: rgb(116, 116, 116) !important;
    border-color: rgb(116, 116, 116) !important;
}

/* --------------知識目錄-------------- */
.catalogfolder {
    content: url("../image/custom-style-01/folder_custom.png") !important;
}
.catalogItem {
    background-color: rgb(116, 116, 116) !important;
}
.catalog {
    background-color: rgb(245 245 245) !important;
}
.catalogBtnUp {
    content: url("../image/custom-style-01/down-arrow_custom.png") !important;
}

/* --------------------對話訊息相關-------------------- */

/* --------------對話框-------------- */
.MessageText {
    background-color: rgb(245 245 245) !important;
    border: 0 !important;
    border-radius: 6px !important;
}

/* --------------查看更多-------------- */
.ViewMoreText {
    background-color: white;
}

/* --------------滿意度按鈕(讚;讚讚;不喜歡;複製)請至../image/custom-style-01/內更換圖片-------------- */
/* 
.讚讚 {
    content: url("../image/custom-style-01/like_custom.png") !important;
    content: url("../image/custom-style-01/likeclick_custom.png") !important;
}
.讚 {
    content: url("../image/custom-style-01/ok_custom.png") !important;
    content: url("../image/custom-style-01/okclick_custom.png") !important;
}
.不喜歡 {
    content: url("../image/custom-style-01/dislike_custom.png") !important;
    content: url("../image/custom-style-01/dislikeclick_custom.png") !important;
}
.複製 {
    content: url("../image/custom-style-01/copy_custom.png") !important;
} 
*/


/* --------------相關問-------------- */
.GptRelated {
    border: rgb(33 37 41) solid 1.5px !important;
    background-color: rgb(255 255 255) !important;
}
.GptRelated:hover {
    background: rgb(245 245 245) !important;
}

/* --------------卡片-------------- */
.card-title {
    margin-bottom: 0.75rem;
    font-size: 1.2rem;
    font-weight: bold;
}
.card-text {
    font-size: 1rem;
}
.card-body li .btn {
    line-height: 1;
}
.card-body li:hover {
    background-color: rgb(180, 201, 232);
}
.card-body li .button:hover {
    color: rgb(23 82 193);
}


/* --------------------------Footer---------------------- */
.sendMessage {
    background-color: rgb(235 235 235) !important;
}
.sendMessageDiv {
    background-color: rgb(255, 255, 255) !important;
    border: rgb(193 193 193) solid 1px !important;
}

/* --------------發問任務圖示-------------- */
.functionTitle {
    color: rgb(0, 0, 0) !important;
}
.catalogBtnDown {
    content: url("../image/custom-style-01/main-menu_custom.png") !important;
}

/* --------------清除主題圖示-------------- */
.cleanIcon {
    content: url("../image/custom-style-01/cleaning_custom.png") !important;
}

/* --------------外部搜尋圖示-------------- */
.knowledgeIconOn {
    content: url("../image/custom-style-01/knowledgeOn_custom.png") !important;
}
.knowledgeIconOff {
    content: url("../image/custom-style-01/knowledgeOff_custom.png") !important;
}

/* --------------字數限制-------------- */
.tokenText {
    color: black !important;
}

/* --------------麥克風圖示-------------- */
.microPhoneIconOn{
    content: url("../image/custom-style-01/RecognitionOn_custom.png") !important;
}
.microPhoneIconOff{
    content: url("../image/custom-style-01/RecognitionOff_custom.png") !important;
}

/* --------------發送圖示-------------- */
.sendIcon {
    content: url("../image/custom-style-01/bot_custom.png") !important;
}

/* --------------語音按鈕-------------- */
.SpeakMessageVolume {
    content: url("../image/custom-style-01/SingalSpeech_custom.png") !important;
}

/* --------------loading-------------- */
.progressDiv {
    background-color: rgb(237, 30, 30) !important;
}

.progress-bar {
    background-color: rgb(0 0 0) !important;
}
#loading font {
    color: rgb(255, 255, 255) !important;
}