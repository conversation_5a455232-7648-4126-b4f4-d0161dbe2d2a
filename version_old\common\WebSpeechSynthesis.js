/**
 * @desc 文字轉語音功能
 *
 * 聲音 ( Chat.PageApp.QbiCopilotInfo.ttsName ) 參數
 * 聲音 ( Chat.PageApp.QbiCopilotInfo.ttsLang ) 參數
 * - 繁體中文：Microsoft Hanhan - Chinese (Traditional, Taiwan) - zh-TW
 * - 繁體中文：Microsoft Yating - Chinese (Traditional, Taiwan) - zh-TW
 * - 繁體中文：Microsoft Zhiwei - Chinese (Traditional, Taiwan) - zh-TW
 * - 繁體中文：Google 國語（臺灣） - zh-TW
 * - 普通話：Google 普通话（中国大陆）- zh-CN
 * - 英文：Google US English - en-US
 * - 印尼語：Google Bahasa Indonesia - id-ID
 * - 泰語：Microsoft Pattara - Thai (Thailand) - th-TH
 * - 越南語：Microsoft An - Vietnamese (Vietnam) - vi-VN
 *
 * 語速 ( rate ) 參數：請輸入介於 0.5 - 2 的數字
 *
 * 語調 ( pitch ) 參數：請輸入介於 0.1 - 2 的數字
 */
var WebSpeechSynthesis = {
    synth: window.speechSynthesis,

    /**
     * @desc  mapping 資料
     */
    isSupport: false,

    /**
 * @desc 定義播音隊列
 */
    utteranceQueue: [],
    /**
     * @desc  mapping 資料
     */
    mappingData: [
        {
            name: "Microsoft Hanhan - Chinese (Traditional, Taiwan)",
            lang: "zh-TW",
        },
        {
            name: "Microsoft Yating - Chinese (Traditional, Taiwan)",
            lang: "zh-TW",
        },
        {
            name: "Microsoft Zhiwei - Chinese (Traditional, Taiwan)",
            lang: "zh-TW",
        },
        {
            name: "Google 國語（臺灣）",
            lang: "zh-TW",
        },
        {
            name: "Google 普通话（中国大陆）",
            lang: "zh-CN",
        },
        {
            name: "Google US English",
            lang: "en-US",
        },
        {
            name: "Google Bahasa Indonesia",
            lang: "id-ID",
        },
        {
            name: "Microsoft Pattara - Thai (Thailand)",
            lang: "th-TH",
        },
        {
            name: "Microsoft An - Vietnamese (Vietnam)",
            lang: "vi-VN",
        },
    ],
    /**
     * @desc 播放文字轉語音
     * @param speakContent 播音文字
     */
    speak: function (speakContent) {
        Chat.speakingCancel();
        if (speakContent !== "") {
            const utterance = new SpeechSynthesisUtterance();
            utterance.text = speakContent;
            utterance.volume = 1;
            utterance.pitch = parseFloat(Chat.PageApp.QbiCopilotInfo.ttsPitch);
            utterance.rate = parseFloat(Chat.PageApp.QbiCopilotInfo.ttsRate);
            utterance.voice = WebSpeechSynthesis.getVoice();
            WebSpeechSynthesis.synth.speak(utterance);
        }
    },
    /**
     * @desc 取得播放聲音對象
     *
     * 語音包優先序： ECP > ECP 同語系之語音 > 瀏覽器預設
     */
    getVoice: function () {
        const voiceName = Chat.PageApp.QbiCopilotInfo.ttsName;
        const voices = WebSpeechSynthesis.synth.getVoices();
        const matchVoice = WebSpeechSynthesis.mappingData.filter((item) => item.name === voiceName);
        let defaultVoice = null;
        let langVoice = [];
        let selectedVoice = null;

        voices.forEach((voice) => {
            const voiceName = Chat.PageApp.QbiCopilotInfo.ttsName;
            if (voice.name === voiceName) selectedVoice = voice;
            if (voice.default === true) defaultVoice = voice;
            if (matchVoice.length === 1 && voice.lang === matchVoice[0].lang) langVoice.push(voice);
        });
        if (!selectedVoice) {
            if (langVoice.length === 0) selectedVoice = defaultVoice;
            else selectedVoice = langVoice[0];
        }
        return selectedVoice;
    },
};
