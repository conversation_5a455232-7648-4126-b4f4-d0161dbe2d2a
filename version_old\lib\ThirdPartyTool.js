var ThirdPartyTool = {
    Version: '*********',

    isValidPath: function (path) {
        const pattern = /^([a-zA-Z0-9_\-\/\.])+$/;
        return pattern.test(path);
    },
    // 插入CSS套件
    includeCSS: function (path) {
        let script = document.createElement('link');
        script.setAttribute('href', path + '?v=' + ThirdPartyTool.Version);
        script.setAttribute('rel', 'stylesheet');
        script.setAttribute('media', 'all');
        document.head.appendChild(script);
    },
    // 插入JS套件
    includeJS: function (path) {
        if (!this.isValidPath(path)) {
            console.error('Invalid path:', path);
            return;
        }
        let script = document.createElement('script');
        script.setAttribute('src', path + '?v=' + ThirdPartyTool.Version);
        document.head.appendChild(script);
    },
    // 插入JS套件
    includeJS_Promise: function (path) {
        return new Promise((resolve, reject) => {
            let script = document.createElement('script');
            script.setAttribute('src', path + '?v=' + ThirdPartyTool.Version);
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    },
    doJudgeLang:function(){
        const language = sessionStorage?.getItem("language")?.toLowerCase();
        const fallbackLanguage = sessionStorage?.getItem("languageChange")?.toLowerCase();
        const { i18n_langMap, ecp_lang, qsEnableCopilot } = parent.parent.EcpController;
        let mappedLanguage = i18n_langMap[fallbackLanguage] || i18n_langMap[language] || Config.i18n_lang;
    
        if (parent.parent.Jui && !qsEnableCopilot) {
            mappedLanguage = language === ecp_lang ? Config.i18n_lang : i18n_langMap[fallbackLanguage] || Config.i18n_lang;
        }
        return mappedLanguage
    }
}

// 對Date的擴展，將 Date 轉化為指定格式的String
// 月(M)、日(d)、小時(h)、分(m)、秒(s)、季度(q) 可以用 1-2 個占位符，
// 年(y)可以用 1-4 個占位符，毫秒(S)只能用 1 個占位符(是 1-3 位的數字)
// 例子：
// (new Date()).Format("yyyy-MM-dd hh:mm:ss.S") ==> 2006-07-02 08:09:04.423
// (new Date()).Format("yyyy-M-d h:m:s.S")      ==> 2006-7-2 8:9:4.18
Date.prototype.Format = function (fmt) {
    var o = {
        "M+": this.getMonth() + 1, //月份
        "d+": this.getDate(), //日
        "H+": this.getHours(), //小時
        "m+": this.getMinutes(), //分
        "s+": this.getSeconds(), //秒
        "q+": Math.floor((this.getMonth() + 3) / 3), //季度
        S: this.getMilliseconds(), //毫秒
    };
    if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(
            RegExp.$1,
            (this.getFullYear() + "").substr(4 - RegExp.$1.length)
        );
    }
    for (var k in o) {
        if (new RegExp("(" + k + ")").test(fmt)) {
            fmt = fmt.replace(
                RegExp.$1,
                RegExp.$1.length == 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length)
            );
        }
    }
    return fmt;
};

// String Format
if (!String.format) {
    String.format = function (format) {
        var args = Array.prototype.slice.call(arguments, 1);
        return format.replace(/{(\d+)}/g, function (match, number) {
            return typeof args[number] != 'undefined'
                ? args[number]
                : match
                ;
        });
    };
}
// Vue
ThirdPartyTool.includeJS('../../lib/vue/vue.js');

// wow css
ThirdPartyTool.includeCSS('../../lib/wow/animate.min.css');

// sweetalert
ThirdPartyTool.includeJS('../../lib/sweetalert/sweetalert2.all.min.js');
// purify
ThirdPartyTool.includeJS('../../lib/purify/purify.min.js');

// jquery載入
ThirdPartyTool.includeJS_Promise('../../lib/jquery/jquery-3.3.1.min.js').then(() => {
    // bootstrap
    ThirdPartyTool.includeJS('../../lib/bootstrap/js/bootstrap.min.js');
    ThirdPartyTool.includeCSS('../../lib/bootstrap/css/bootstrap.min.css');
}).catch((error) => {
    console.error("Error loading scripts");
});;

// highlight
ThirdPartyTool.includeCSS('../../lib/highlight/highlight.min.css');
ThirdPartyTool.includeJS('../../lib/highlight/highlight.min.js');

// swiper
ThirdPartyTool.includeJS('../../lib/swiper/js/swiper-element-bundle.min.js');
ThirdPartyTool.includeJS('../../lib/swiper/js/swiper-bundle.min.js');
ThirdPartyTool.includeCSS('../../lib/swiper/js/swiper-bundle.min.css');

// vconsole
ThirdPartyTool.includeJS('../../lib/vconsole/vconsole.min.js');


// 同步加载 Config.js > i18n
ThirdPartyTool.includeJS_Promise('../../config/Config.js').then(() => {
    
    ThirdPartyTool.includeJS('../../config/i18n/' + ThirdPartyTool.doJudgeLang() + '.js');
    if (Config.CUSTOM_CSS_ENABLE) {
        ThirdPartyTool.includeCSS('../../custom/' + Config.CUSTOM_CSS_PATH);
    }
}).catch(() => {
    console.error("Error loading scripts");
});

// 基本設定
ThirdPartyTool.includeJS('../../common/CommonUtil.js');
ThirdPartyTool.includeJS('../../common/HttpQuery.js');
ThirdPartyTool.includeJS('../../common/UserInfo.js');

// 基本樣式
ThirdPartyTool.includeCSS('../../css/main.css');

//瀏覽器語音API
ThirdPartyTool.includeJS('../../common/WebSpeechRecognition.js');
ThirdPartyTool.includeJS('../../common/WebSpeechSynthesis.js');

//Audio-Wave API
ThirdPartyTool.includeJS('../../lib/siriwave-master/siriwave.umd.min.js');

// 會出Excel
ThirdPartyTool.includeJS('../../common/ExportExcel.js');


