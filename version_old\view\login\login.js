var Login = {

    MODE_USER: parent.Main.MODE_USER,
    MODE_MEMBER: parent.Main.MODE_MEMBER,
    LANGUAGE: "zh-tw",

    doLoad() {
        this.doCheckSystem();
        this.initApp();
        this.initIcon();
        this.initLoginMode();
        this.doCheckLogin();
        this.doShowLanguageChange();
    },

    initApp() {
        Login.PageApp = new Vue({
            el: "#App",
            data: {
                T: Text,
                account: '',
                pd: '',
                UserLogin: false,
                MemberLogin: false,
                AnonymousLogin: false,
                languageTextMap:{"zh-TW":"繁體中文","en-US":"English","ja-JP":"日本語","zh-CN":"简体中文"},
                selectedLanguage: sessionStorage?.getItem("languageChange")|| sessionStorage?.getItem("language")|| Config.i18n_lang,
                i18n_langArray:Config.i18n_langArray,
                LoginMode: Config.LOGIN_MODE //member、user、anonymous
            },
            methods: {
                doToggle(event) {
                    Main.doToggle();
                },
                doLogin(mode) {
                    Login.doLogin(mode);
                },
                changeLanguage(Language){
                    Login.changeLanguage(Language);
                }
            },
            watch: {},
        });
        parent.Main.showLoading(false, 0);
    },

    doShowLanguageChange() {
        if(sessionStorage?.getItem("languageChange") !== sessionStorage?.getItem("language")){
            sessionStorage.setItem("language",sessionStorage?.getItem("languageChange"));
            parent.parent.EcpController.SHOW_TOGGLE=false;
            parent.parent.EcpController.doToggle();
        }
    },
    initLoginMode() {
        let mode = Config.LOGIN_MODE;
        if (Config.DETECTION_LOGIN) {
            let queryString = parent.parent.window.location.search;
            let urlMode = CommonUtil.getURLParameter(queryString, 'login');
            if (urlMode != null) {
                mode = urlMode;
            }
        }
        Login.PageApp.AnonymousLogin = Config.ENABLE_ANONYMOUS;
        switch (mode) {
            case Login.MODE_MEMBER:
                parent.Main.PageApp.userTypeContent = Text['memberName'];
                Login.PageApp.MemberLogin = true;
                break;
            case Login.MODE_USER:
                parent.Main.PageApp.userTypeContent = Text['innerUserName'];
                Login.PageApp.UserLogin = true;
                break;
        }
        Login.PageApp.LoginMode = mode;
        CommonUtil.setLocalStorage('QbiCopilotLoginMode', mode);
    },

    initIcon() {
        parent.parent.$('#' + parent.parent.EcpController.KM_COPILOT_ICON_ID).show(250);
        parent.$('#userIcon').hide();
        parent.$('#historyIcon').hide(100);
        parent.$('#mainFontSize').hide();
    },

    doLogin(mode) {
        switch (mode) {
            case Login.MODE_MEMBER:
                EcpMemberLogin.doLogin();
                break;
            case Login.MODE_USER:
                EcpUserLogin.doLogin();
                break;
            default:
                let userInfoData = { userName: Text['Anonymous'], userId: CommonUtil.getRandomUuid(), mode: 'anonymous' };
                parent.Main.PageApp.UserInfo.userName = userInfoData.userName;
                parent.Main.PageApp.UserInfo.userMode = userInfoData.mode;
                parent.Main.PageApp.showLeave = true;
                CommonUtil.setLocalStorage('UserInfo', JSON.stringify(userInfoData));
                Login._loginSuccess();
        }
    },

    doCheckLogin() {
        setTimeout(function () {
            let soure = CommonUtil.getLocalStorage('source');
            if ('teams' == soure) {
                Login.doLogin('anonymous')
            }
            let userInfo = CommonUtil.getLocalStorage('UserInfo');
            if (userInfo?.tokenData) {
                switch (userInfo.mode) {
                    case Login.MODE_MEMBER:
                        EcpMemberLogin.doReadyLogin(userInfo.tokenData.contact.id, userInfo.tokenData.contact.name, userInfo.tokenData.tokenId);
                        break;
                    case Login.MODE_USER:
                        EcpUserLogin.doReadyLogin(userInfo.tokenData.user.id, userInfo.tokenData.user.name, userInfo.tokenData.tokenId);
                        break;
                }
            }
        }, 500);
    },

    doCheckSystem() {
        HttpQuery.doRequest('/openapi/copilot/getCopilotBaseInfo', {}, function (result) {
            if (result.enable && result.isReady) {
                CommonUtil.setLocalStorage('QbiCopilotInfo', JSON.stringify(result));
                CommonUtil.setLocalStorage('QbiCopilotBaseInfo', JSON.stringify(result));
            } else {
                $('#account').attr('disabled', 'disabled');
                $('#pd').attr('disabled', 'disabled');
                Login.PageApp.AnonymousLogin = false;
                Login.PageApp.UserLogin = false;
                Login.PageApp.MemberLogin = false;
                parent.Main.doNotReady();
                CommonUtil.deleteLocalStorage('UserInfo');
            }
        });
    },
    
    changeLanguage(Language){
        sessionStorage.setItem("languageChange",Language);
        const kmCoPilot = parent.parent.window.document.getElementById(parent.parent.EcpController.KM_COPILOT_ID);
        const kmCoPilotWeb = parent.parent.window.document.getElementById(parent.parent.EcpController.KM_COPILOT_WEB_ID);
        const kmCoPilotHistory = parent.parent.window.document.getElementById(parent.parent.EcpController.KM_COPILOT_HISTORY_ID);
            
        //畫面顯示優化
        kmCoPilot.style.display = 'none';
        kmCoPilotWeb.style.display = 'none';
        kmCoPilotHistory.style.display = 'none';
        kmCoPilotHistory.contentWindow.location.reload();
        kmCoPilot.contentWindow.location.reload();
        kmCoPilotWeb.contentWindow.location.reload();
    },

    _loginSuccess() {
        parent.$('#userIcon').show(100);
        parent.$('#historyIcon').show(100);
        parent.$('#mainFontSize').show(100);
        
        parent.Main.goPage(parent.ViewController.PAGE.CHAT);
    }
}