!function(t,i){"object"==typeof exports&&"undefined"!=typeof module?module.exports=i():"function"==typeof define&&define.amd?define(i):(t="undefined"!=typeof globalThis?globalThis:t||self).SiriWave=i()}(this,(function(){"use strict";
/*! *****************************************************************************
    Copyright (c) Microsoft Corporation.

    Permission to use, copy, modify, and/or distribute this software for any
    purpose with or without fee is hereby granted.

    THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
    PERFORMANCE OF THIS SOFTWARE.
    ***************************************************************************** */var t=function(){return t=Object.assign||function(t){for(var i,s=1,e=arguments.length;s<e;s++)for(var o in i=arguments[s])Object.prototype.hasOwnProperty.call(i,o)&&(t[o]=i[o]);return t},t.apply(this,arguments)};var i=function(){function t(t,i){this.ATT_FACTOR=4,this.GRAPH_X=2,this.AMPLITUDE_FACTOR=.6,this.ctrl=t,this.definition=i}return t.prototype.globalAttFn=function(t){return Math.pow(this.ATT_FACTOR/(this.ATT_FACTOR+Math.pow(t,this.ATT_FACTOR)),this.ATT_FACTOR)},t.prototype.xPos=function(t){return this.ctrl.width*((t+this.GRAPH_X)/(2*this.GRAPH_X))},t.prototype.yPos=function(t){return this.AMPLITUDE_FACTOR*(this.globalAttFn(t)*(this.ctrl.heightMax*this.ctrl.amplitude)*(1/this.definition.attenuation)*Math.sin(this.ctrl.opt.frequency*t-this.ctrl.phase))},t.prototype.draw=function(){var t=this.ctrl.ctx;t.moveTo(0,0),t.beginPath();var i=(this.definition.color||this.ctrl.color).replace(/rgb\(/g,"").replace(/\)/g,"");t.strokeStyle="rgba(".concat(i,",").concat(this.definition.opacity,")"),t.lineWidth=this.definition.lineWidth;for(var s=-this.GRAPH_X;s<=this.GRAPH_X;s+=this.ctrl.opt.pixelDepth)t.lineTo(this.xPos(s),this.ctrl.heightMax+this.yPos(s));t.stroke()},t.getDefinition=function(){return[{attenuation:-2,lineWidth:1,opacity:.1},{attenuation:-6,lineWidth:1,opacity:.2},{attenuation:4,lineWidth:1,opacity:.4},{attenuation:2,lineWidth:1,opacity:.6},{attenuation:1,lineWidth:1.5,opacity:1}]},t}(),s=function(){function t(t,i){this.GRAPH_X=25,this.AMPLITUDE_FACTOR=.8,this.SPEED_FACTOR=1,this.DEAD_PX=2,this.ATT_FACTOR=4,this.DESPAWN_FACTOR=.02,this.DEFAULT_NOOFCURVES_RANGES=[2,5],this.DEFAULT_AMPLITUDE_RANGES=[.3,1],this.DEFAULT_OFFSET_RANGES=[-3,3],this.DEFAULT_WIDTH_RANGES=[1,3],this.DEFAULT_SPEED_RANGES=[.5,1],this.DEFAULT_DESPAWN_TIMEOUT_RANGES=[500,2e3],this.ctrl=t,this.definition=i,this.noOfCurves=0,this.spawnAt=0,this.prevMaxY=0,this.phases=[],this.offsets=[],this.speeds=[],this.finalAmplitudes=[],this.widths=[],this.amplitudes=[],this.despawnTimeouts=[],this.verses=[]}return t.prototype.getRandomRange=function(t){return t[0]+Math.random()*(t[1]-t[0])},t.prototype.spawnSingle=function(t){var i,s,e,o,n,h,r,a,p,l;this.phases[t]=0,this.amplitudes[t]=0,this.despawnTimeouts[t]=this.getRandomRange(null!==(s=null===(i=this.ctrl.opt.ranges)||void 0===i?void 0:i.despawnTimeout)&&void 0!==s?s:this.DEFAULT_DESPAWN_TIMEOUT_RANGES),this.offsets[t]=this.getRandomRange(null!==(o=null===(e=this.ctrl.opt.ranges)||void 0===e?void 0:e.offset)&&void 0!==o?o:this.DEFAULT_OFFSET_RANGES),this.speeds[t]=this.getRandomRange(null!==(h=null===(n=this.ctrl.opt.ranges)||void 0===n?void 0:n.speed)&&void 0!==h?h:this.DEFAULT_SPEED_RANGES),this.finalAmplitudes[t]=this.getRandomRange(null!==(a=null===(r=this.ctrl.opt.ranges)||void 0===r?void 0:r.amplitude)&&void 0!==a?a:this.DEFAULT_AMPLITUDE_RANGES),this.widths[t]=this.getRandomRange(null!==(l=null===(p=this.ctrl.opt.ranges)||void 0===p?void 0:p.width)&&void 0!==l?l:this.DEFAULT_WIDTH_RANGES),this.verses[t]=this.getRandomRange([-1,1])},t.prototype.getEmptyArray=function(t){return new Array(t)},t.prototype.spawn=function(){var t,i;this.spawnAt=Date.now(),this.noOfCurves=Math.floor(this.getRandomRange(null!==(i=null===(t=this.ctrl.opt.ranges)||void 0===t?void 0:t.noOfCurves)&&void 0!==i?i:this.DEFAULT_NOOFCURVES_RANGES)),this.phases=this.getEmptyArray(this.noOfCurves),this.offsets=this.getEmptyArray(this.noOfCurves),this.speeds=this.getEmptyArray(this.noOfCurves),this.finalAmplitudes=this.getEmptyArray(this.noOfCurves),this.widths=this.getEmptyArray(this.noOfCurves),this.amplitudes=this.getEmptyArray(this.noOfCurves),this.despawnTimeouts=this.getEmptyArray(this.noOfCurves),this.verses=this.getEmptyArray(this.noOfCurves);for(var s=0;s<this.noOfCurves;s++)this.spawnSingle(s)},t.prototype.globalAttFn=function(t){return Math.pow(this.ATT_FACTOR/(this.ATT_FACTOR+Math.pow(t,2)),this.ATT_FACTOR)},t.prototype.sin=function(t,i){return Math.sin(t-i)},t.prototype.yRelativePos=function(t){for(var i=0,s=0;s<this.noOfCurves;s++){var e=4*(s/(this.noOfCurves-1)*2-1);e+=this.offsets[s];var o=t*(1/this.widths[s])-e;i+=Math.abs(this.amplitudes[s]*this.sin(this.verses[s]*o,this.phases[s])*this.globalAttFn(o))}return i/this.noOfCurves},t.prototype.yPos=function(t){return this.AMPLITUDE_FACTOR*this.ctrl.heightMax*this.ctrl.amplitude*this.yRelativePos(t)*this.globalAttFn(t/this.GRAPH_X*2)},t.prototype.xPos=function(t){return this.ctrl.width*((t+this.GRAPH_X)/(2*this.GRAPH_X))},t.prototype.drawSupportLine=function(){var t=this.ctrl.ctx,i=[0,this.ctrl.heightMax,this.ctrl.width,1],s=t.createLinearGradient.apply(t,i);s.addColorStop(0,"transparent"),s.addColorStop(.1,"rgba(255,255,255,.5)"),s.addColorStop(.8,"rgba(255,255,255,.5)"),s.addColorStop(1,"transparent"),t.fillStyle=s,t.fillRect.apply(t,i)},t.prototype.draw=function(){var t=this.ctrl.ctx;if(t.globalAlpha=.7,t.globalCompositeOperation=this.ctrl.opt.globalCompositeOperation,0===this.spawnAt&&this.spawn(),this.definition.supportLine)return this.drawSupportLine();for(var i=0;i<this.noOfCurves;i++)this.spawnAt+this.despawnTimeouts[i]<=Date.now()?this.amplitudes[i]-=this.DESPAWN_FACTOR:this.amplitudes[i]+=this.DESPAWN_FACTOR,this.amplitudes[i]=Math.min(Math.max(this.amplitudes[i],0),this.finalAmplitudes[i]),this.phases[i]=(this.phases[i]+this.ctrl.speed*this.speeds[i]*this.SPEED_FACTOR)%(2*Math.PI);for(var s=-1/0,e=0,o=[1,-1];e<o.length;e++){var n=o[e];t.beginPath();for(var h=-this.GRAPH_X;h<=this.GRAPH_X;h+=this.ctrl.opt.pixelDepth){var r=this.xPos(h),a=this.yPos(h);t.lineTo(r,this.ctrl.heightMax-n*a),s=Math.max(s,a)}t.closePath(),t.fillStyle="rgba(".concat(this.definition.color,", 1)"),t.strokeStyle="rgba(".concat(this.definition.color,", 1)"),t.fill()}return s<this.DEAD_PX&&this.prevMaxY>s&&(this.spawnAt=0),this.prevMaxY=s,null},t.getDefinition=function(){return[{color:"255,255,255",supportLine:!0},{color:"15, 82, 169"},{color:"173, 57, 76"},{color:"48, 220, 155"}]},t}();return function(){function e(e){var o=this,n=e.container,h=function(t,i){var s={};for(var e in t)Object.prototype.hasOwnProperty.call(t,e)&&i.indexOf(e)<0&&(s[e]=t[e]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(e=Object.getOwnPropertySymbols(t);o<e.length;o++)i.indexOf(e[o])<0&&Object.prototype.propertyIsEnumerable.call(t,e[o])&&(s[e[o]]=t[e[o]])}return s}(e,["container"]);this.phase=0,this.run=!1,this.curves=[];var r=window.getComputedStyle(n);this.opt=t({container:n,style:"ios",ratio:window.devicePixelRatio||1,speed:.2,amplitude:1,frequency:6,color:"#fff",cover:!1,width:parseInt(r.width.replace("px",""),10),height:parseInt(r.height.replace("px",""),10),autostart:!0,pixelDepth:.02,lerpSpeed:.1,globalCompositeOperation:"lighter"},h),this.speed=Number(this.opt.speed),this.amplitude=Number(this.opt.amplitude),this.width=Number(this.opt.ratio*this.opt.width),this.height=Number(this.opt.ratio*this.opt.height),this.heightMax=Number(this.height/2)-6,this.color="rgb(".concat(this.hex2rgb(this.opt.color),")"),this.interpolation={speed:this.speed,amplitude:this.amplitude},this.canvas=document.createElement("canvas");var a=this.canvas.getContext("2d");if(null===a)throw new Error("Unable to create 2D Context");if(this.ctx=a,this.canvas.width=this.width,this.canvas.height=this.height,!0===this.opt.cover?this.canvas.style.width=this.canvas.style.height="100%":(this.canvas.style.width="".concat(this.width/this.opt.ratio,"px"),this.canvas.style.height="".concat(this.height/this.opt.ratio,"px")),"ios9"===this.opt.style)this.curves=(this.opt.curveDefinition||s.getDefinition()).map((function(t){return new s(o,t)}));else this.curves=(this.opt.curveDefinition||i.getDefinition()).map((function(t){return new i(o,t)}));this.opt.container.appendChild(this.canvas),this.opt.autostart&&this.start()}return e.prototype.hex2rgb=function(t){t=t.replace(/^#?([a-f\d])([a-f\d])([a-f\d])$/i,(function(t,i,s,e){return i+i+s+s+e+e}));var i=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(t);return i?"".concat(parseInt(i[1],16).toString(),",").concat(parseInt(i[2],16).toString(),",").concat(parseInt(i[3],16).toString()):null},e.prototype.intLerp=function(t,i,s){return t*(1-s)+i*s},e.prototype.lerp=function(t){var i=this.interpolation[t];return null!==i&&(this[t]=this.intLerp(this[t],i,this.opt.lerpSpeed),this[t]-i==0&&(this.interpolation[t]=null)),this[t]},e.prototype.clear=function(){this.ctx.globalCompositeOperation="destination-out",this.ctx.fillRect(0,0,this.width,this.height),this.ctx.globalCompositeOperation="source-over"},e.prototype.draw=function(){this.curves.forEach((function(t){return t.draw()}))},e.prototype.startDrawCycle=function(){this.clear(),this.lerp("amplitude"),this.lerp("speed"),this.draw(),this.phase=(this.phase+Math.PI/2*this.speed)%(2*Math.PI),window.requestAnimationFrame?this.animationFrameId=window.requestAnimationFrame(this.startDrawCycle.bind(this)):this.timeoutId=setTimeout(this.startDrawCycle.bind(this),20)},e.prototype.start=function(){if(!this.canvas)throw new Error("This instance of SiriWave has been disposed, please create a new instance");this.phase=0,this.run||(this.run=!0,this.startDrawCycle())},e.prototype.stop=function(){this.phase=0,this.run=!1,this.animationFrameId&&window.cancelAnimationFrame(this.animationFrameId),this.timeoutId&&clearTimeout(this.timeoutId)},e.prototype.dispose=function(){this.stop(),this.canvas&&(this.canvas.remove(),this.canvas=null)},e.prototype.set=function(t,i){this.interpolation[t]=i},e.prototype.setSpeed=function(t){this.set("speed",t)},e.prototype.setAmplitude=function(t){this.set("amplitude",t)},e}()}));
