var QbiMediaCard = {
  create(content) {
    var cardBtn = "";
    var viewHtml = "";
    var videoTypeAry = ["mp4"];
    var imageTypeAry = ["jpg", "jpeg", "png"];
    var contentUrlAry = content.originalContentUrl.split(".");
    var fileType = contentUrlAry[contentUrlAry.length - 1].toLowerCase();

    // 根據 mediaType 處理內容
    switch (content.mediaType) {
      case "image":
        viewHtml = 
          "<a href='" + content.originalContentUrl + "' target='_blank'>" +
          "<img class='cardImage' title='" +
          (content.mediaMode == "url" ? Text["pictureLink"] : content.name) +
          "' style='width:100%;' src='" +
          content.originalContentUrl +
          "' alt=\"" +
          (content.mediaMode == "url" ? Text["pictureLink"] : content.name) +
          '" />' +
          "</a>";
        break;
      case "video":
        if (content.mediaMode == "url") {
          if (content.originalContentUrl.toLowerCase().includes("https://www.youtube.com")) {
            viewHtml =
              "<iframe style='width:100%; height: auto;' src='" +
              content.originalContentUrl +
              "' allowfullscreen></iframe>";
          } else {
            if (videoTypeAry.includes(fileType)) {
              viewHtml =
                "<video controls title='" +
                Text["videoLink"] +
                "' style='width:100%;' src='" +
                content.originalContentUrl +
                "' alt=\"" +
                Text["videoLink"] +
                '" ></video>';
            } else {
              viewHtml =
                "<div class='divCenter'>" +
                "<a href='" +
                content.originalContentUrl +
                "' target='_blank'>" +
                Text["PH_LinkToUrlPage"] +
                "</a>" +
                "</div>";
            }
          }
        } else {
          viewHtml =
            "<video controls title='" +
            content.name +
            Text["videoLink"] +
            "' style='width:100%;' src='" +
            content.originalContentUrl +
            "' alt=\"" +
            content.name +
            '" ></video>';
        }
        break;
    }

    /** 組成卡片按鈕 */
    for (var j = 0; j < content.FQACardAnswer.length; j++) {
      var QACardAnswer = content.FQACardAnswer[j];
      var name = QACardAnswer.FCode || QACardAnswer.FName;
      switch (QACardAnswer.Option) {
        case "Option":
          var card_code = encodeURIComponent(
            JSON.stringify({
              FCode: name,
              FDisplayText: QACardAnswer.FDisplayText,
              FShowText: QACardAnswer.FShowText,
            })
          );
          cardBtn +=
            `<li class='swiper_li' tabindex="0" ` +
            `onKeypress="ChatEvent.onAnswerButtonClick('Option','` +
            card_code +
            `');" onclick="ChatEvent.onAnswerButtonClick('Option','` +
            card_code +
            `');" >` +
            QACardAnswer.FShowText +
            "</li>";
          break;
        case "Url":
          cardBtn +=
            "<li class='swiper_li'>" +
            "<a tabindex=\"0\" onclick=ChatEvent.onAnswerButtonClick('Url','" +
            encodeURIComponent(name) +
            "') target='_blank'>" +
            QACardAnswer.FShowText +
            "</a>" +
            "</li>";
          break;
      }
    }

    var contentHtml =
      "<div>" +
      "   <div class='cardsSlide'>" +
      viewHtml +
      "   <ul class='swiper_ul'>" +
      cardBtn +
      "   </ul>" +
      "   </div>" +
      "</div>";
    return contentHtml;
  },
};
