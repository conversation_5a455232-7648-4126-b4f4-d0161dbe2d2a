html, body {
    height: 100%;
    margin: 0;
    overflow: hidden;
  }
  
  .historyPanel {
    position: absolute;
    top: 80px;
    left: 0;
    right: 0;
    bottom: 0;
    width: 250px;
    background-color: #fafafa;
    overflow-y: auto;
  }
  
  .historyList {
    list-style: none;
    margin: 0;
    padding: 10px;
  }
  
  .historyItem {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    font-size: 14px;
    padding: 8px 12px;
    border-radius: 6px;
    transition: background-color 0.2s;
  }
  
  .historyItem:hover,
  .historyItemClicked {
    background-color: #eee;
  }
  
  .historyItemContent {
    width: 100%;
    min-height: 15px;
    transition: background-color 0.2s;
  }
  
  .dotsBtn {
    display: none;
    width: 10px;
    height: 10px;
    background: transparent;
    border: none;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    justify-content: center;
    align-items: center;
    color: #3f3f3f;
    flex-shrink: 0;
  }
  .dotsBtn:hover {
    color: #000000;
  }
  
  .historyItem:hover .dotsBtn {
    display: flex;
  }
  
  .textWithMenu {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
  
  .itemText {
    flex: 1;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    margin-right: 8px;
  }
  
  .itemMenu {
    position: absolute;
    top: 30px;
    right: 5px;
    width: 100px;
    padding: 8px;
    background-color: #ffffff;
    border: 1px solid #ccc;
    border-radius: 8px;
    box-shadow: 0 5px 8px rgba(0, 0, 0, 0.2);
    z-index: 9999;
  }
  
  .menuItem {
    border-radius: 8px;
    padding: 5px 10px;
    cursor: pointer;
    font-size: 14px;
  }
  
  .menuItem:hover {
    background-color: #e6e6e6;
  }
  
  .historyItemEdit {
    display: flex;
    align-items: center;
    gap: 5px;
  }
  
  .renameInput {
    flex: 1;
    padding: 3px 6px;
    font-size: 14px;
  }
  
  .categorizeText {
    font-size: 15px;
    color: #333;
    margin-left: 12px;
    font-weight: bold;
    padding-top: 20px;
    padding-bottom: 9px;
  }
  
  .navBar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 40px;
    padding-left: 10px;
    display: flex;
    align-items: center;
    background-color: rgb(139, 168, 217);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    z-index: 99999;
  }
  
  .title {
    font-size: 15px;
    color: white;
    font-weight: bold;
  }
  
  .loadingIndicator {
    display: flex;
    justify-content: center;
  }
  
  .ChatAddBtn {
    border: hidden;
    width: 40px;
    height: 40px;
    border: 10px;
    display: flex;
    align-items: center;
    justify-content: start;
    cursor: pointer;
    font-size: 16px;
    background-color: #fafafa;
  }
  