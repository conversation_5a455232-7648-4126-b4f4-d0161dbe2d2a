<!DOCTYPE html>
<html>

<head>
    <meta name="viewport" charset="utf-8"
        content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />

    <!-- 禁用快取 -->
    <meta http-equiv="Expires" content="0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Cache-control" content="no-cache">
    <meta http-equiv="Cache" content="no-cache">

    <!-- 載入必要lib -->
    <script src="../../lib/ThirdPartyTool.js" charset="utf-8" async></script>

    <link rel=stylesheet type="text/css" href="./webView.css" charset="utf-8">


    <script>
        document.oncontextmenu = new Function("return false");
        oncontextmenu = "return false;"
    </script>
</head>

<body onload="WebView.doLoad()">

    <!-- 主要程式邏輯 -->
    <script src="webView.js" charset="utf-8"></script>
    <script src="../chat/chatEvent.js" charset="utf-8"></script>

    <div id="App">
        <nav class="navbar navbar-light justify-content-center navBar">
            <a class="animate__animated animate__fadeIn webviewback" v-if="IsBackShow" v-on:click="doBackList"><img
                  class="webviewbackIcon"   src="../../image/back.png"></a>
            <font class="title" v-bind:title="WebTitle">{{WebTitle.substr(0,45)+(WebTitle.length>45?'...':'')}}</font>
            <a class="close" v-on:click="doCloseView"><img class="closeIcon" src="../../image/cancel.png"></a>
        </nav>
        <div v-if="'LIST' == Mode" class="animate__animated animate__fadeIn mainView">
            <p>
                <font class="question">{{T['question']}}{{InputQuestion}}</font>
            </p>
            <p>{{T['viewMoreTitle']}}</p>
            <div class="card m-2" v-for="(item, index) in ListData">
                <div class="card-header">
                    <div>
                        <font style="cursor: pointer;color: #007bff;"
                            v-on:click="doOpenDetail('km',item.metadata.sourceName,item.metadata.knowledgeId)">
                            {{item.metadata.sourceName}}</font><img style="cursor: pointer;margin-left:5px"
                            v-if="item.metadata.type=='file'" src="../../image/file.png" width="15px"
                            v-on:click="doOpenDetail('file',item.metadata.sourceName,item.metadata.sourceId)">
                    </div>
                </div>
                <div class="card-body">
                    <p class="card-text" v-html="item.page_content"></p>
                </div>
            </div>
        </div>
        <div v-if="'HTML' == Mode" class="animate__animated animate__fadeIn mainView">
            <div v-if="attachments.length>0">
                <p>{{T['downloadList']}}</p>
                <table class="table table-sm text-center" style="table-layout: fixed;">
                    <thead>
                        <tr style="background-color:rgba(139, 168, 217, 0.3); font-size:14px">
                            <th scope="col" style="width:30px;"></th>
                            <th scope="col" style="width:95px;">操作</th>
                            <th scope="col">{{T['fileName']}}</th>
                            <th scope="col" style="width:160px;">{{T['fileUploadTime']}}</th>
                            <th scope="col" style="width:100px;">{{T['fileSize']}}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="(item, index) in attachments">
                            <th scope="row" >{{index+1}}</th>
                            <td >
                                <div class="d-flex justify-content-between" style="padding-left: 6px;padding-right: 6px;">
                                    <a href="#"
                                       v-if="item.FName.toLowerCase().includes('.pdf')"
                                       @click="downloadFile(item.FId, getFileNameWithoutExt(item.FName), true)">
                                       閱覽
                                    </a>
                                    <span v-else class="text-secondary">閱覽</span>
                                    <a href="#"
                                       @click="downloadFile(item.FId, getFileNameWithoutExt(item.FName), false)">下載</a>
                                </div>
                            </td>
                            <td v-bind:title="item.FName" class="text-truncate text-left">
                                {{item.FName}}
                            </td>
                            <td>{{new Date(item.FUploadTime).Format("yyyy-MM-dd HH:mm:ss")}}</td>
                            <td>{{Number.parseFloat(item.FSize / 1024).toFixed(2)}} KB</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div id="mainFrame" v-html="HtmlView"></div>
        </div>
        <iframe v-if="'URL' == Mode" class="animate__animated animate__fadeIn mainWebView" id="mainWebFrame"
            v-bind:src="WebViewSrc"></iframe>
     </div>
    </div>
</body>

</html>