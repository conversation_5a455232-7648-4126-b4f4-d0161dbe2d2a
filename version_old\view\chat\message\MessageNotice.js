var MessageNotice = {

    template: `
        <image class="MessageNotice" src="../../image/check-mark.png" width="30px" style="margin-right:5px"></image> 
        <font id="{UUID}" style="word-wrap: break-word;">{content}</font>`,

    create(args) {
        if (typeof args == "object") {
            let html = MessageNotice.template;
            html = html.replace('{UUID}', args.noticeId);
            html = html.replace('{content}', args.content);
            return html;
        } else {
            let html = MessageNotice.template;
            html = html.replace('id="{UUID}"', "");
            html = html.replace('{content}', args);
            return html;
        }
    }
}