var Text = {
    "Anonymous": "Anonymous Use",
    "answerEmpty": "Sorry, this knowledge has no set answer and cannot respond at this time.",
    "answering": "Generating an answer for you ..",
    "streamAnswering": "No knowledge in the knowledge base, searching the web for you ..",
    "audioCaptureError": "Microphone not found. Please ensure a microphone is installed and configured correctly",
    "autoDetect": "Auto Detect",
    "account": "Account",
    "account_desc": "Please enter your account",
    "audio_CopyShowText": "【Audio】 Click To Play",
    "audio": "This is an audio。",
    "browserSupportError": "This browser does not support the Web Speech API",
    "catalog": "Select Knowledge Directory",
    "catalogNone": "No Subdirectory",
    "catalogNotice": "You can only view up to the third-level directory",
    "catalogNow": "Current Directory",
    "chatError": "Oops... Sorry, error occurred. Please try again later.",
    "chatError429": "The system is currently busy. Please try again later.",
    "chatError400": "Regarding your question, we are unable to respond due to the involvement of some sensitive information.",
    "chatNotFound": "Sorry, we couldn't find the answer you're looking for...",
    "clearTaskInfo": "Clear memory when switching tasks",
    "createNewChat": "New Chat Room",
    "dataSourceText": "Data Source:",
    "defaultCatalog": "All",
    "displayBig": "Large",
    "displayNormal": "Medium",
    "displaySize": "Display Size",
    "displaySmall": "Small",
    "Download": "Download",
    "downloadList": "Attachment Download List:",
    "dropDownI18nText_Select": "Select Language", 
    "errorContent": "Sorry, something went wrong...",
    "error_inhibit": "Sorry, this service is not available...",
    "errorLogin": "Login Failed",
    "errorLoginContent": "Please check if your account and password are correct",
    "errorNotReadyContent": "Please check if the parameters are completed.",
    "errorNotReadyTitle": "System Not Ready",
    "errorTitle": "Oops...",
    "fileName": "File Name",
    "fileSize": "File Size (KB)",
    "fileUploadTime": "Upload Time",
    "fontSize": "Font Size",
    "functionButtonHint": "Inquiry Task",
    "functionLoading": "Loading Task",
    "functionTask": "Inquiry Task",
    "file_CopyShowText": "【FileDownload】 ",
    "file": "file。",
    "gptAlert": "Answer generated by GPT",
    "GoogleAlert": "Data Source (Search Engine)",
    "historyTitle": "Chat History",
    "inputCannotBeEmpty": "Please enter your feedback content",
    "innerUserName": "Employee:",
    "insideKnowledgeButtonHint": "Internal Query Priority",
    "image": "This is an image。",
    "keyWordNotFonud_end": "”, please try asking in another way.",
    "keyWordNotFonud_front": "Sorry, I cannot understand the intent of “",
    "knowledgeEmpty": "Knowledge content is not yet filled in.",
    "leave": "Leave",
    "loginFailDesc": "Please log in again, redirecting to login page in 3 seconds",
    "loginFailTitle": "Login Expired",
    "logout": "Logout",
    "Memberlogin": "Member Login",
    "memberName": "Member:",
    "microAccessError": "Microphone access denied",
    "newChat": "New Chat",
    "noSpeechError": "No speech detected, you may need to adjust your microphone settings",
    "notAllowSpeechError": "Microphone access denied",
    "OFF": "OFF",
    "OK": "Confirm",
    "ON": "ON",
    "outerKnowledgeButtonHint": "External Query",
    "notSupportformat": "This format is not supported.",
    "pd": "Password",
    "pd_desc": "Please enter your password",
    "pinCopilot": "Pin Screen",
    "please_wait": "Please wait, loading...",
    "pictureLink": "Picture Link",
    "PH_LinkToUrlPage": "Please click to go to the link page",
    "question": "Question:",
    "quickReplyDirectionError": "Please set the correct Config.quickReplyDirection parameter",
    "referDesc": "Please refer to the knowledge base or click the options below to continue asking",
    "referTitle": "You might want to ask the following questions",
    "referenceError": "Sorry, unable to answer your question at this time",
    "relatedPreWord": "You might want to know:",
    "roomDeleteText": "Delete",
    "roomReNameText": "Rename",
    "satisfactCancel": "Cancel",
    "satisfactCopyText": "Copy",
    "satisfactDislikeText": "Dislike",
    "satisfactLikeText": "Super Like",
    "satisfactOKText": "Like",
    "satisfactResult": "Thank you, we have received your feedback",
    "satisfactSend": "Submit",
    "satisfactTitle": "Feedback",
    "searching": "Searching:",
    "searchSourceText": "DataSource (Web Search):",
    "sendIconHint": "Send",
    "sendMessage": "What would you like to ask?",
    "SpeakMessageVolumeHint": "Voice Playback Button",
    "SpeechToTextBtnHint": "Voice Input",
    "title": "Ai3 Assistant",
    "taskError": "The task is currently being adjusted or no analyzable data is available. Please refresh the page or revise your question.",
    "UploadStart": "Uploading...",
    "userIconHint": "User Information",
    "userInfo": "User Information",
    "userName": "Name:",
    "Userlogin": "Employee Login",
    "VideoType": "VideoPlayer",
    "viewMore": "View More",
    "viewMoreTitle": "Here are the results for your query:",
    "videoLink": "Video Link",
    "video_CopyShowText": "【Video】 Click To Play",
    "video": "This is a video。",
    "webSpeechSynthesis": "Automatic Play Voice"
}
