var EcpUserLogin = {
    doLogin() {
        HttpQuery.doRequest('/openapi/qs/user/token/apply', {
            loginName: Login.PageApp.account,
            password: Login.PageApp.pd,
            language: Login.LANGUAGE
        }, function (result) {
            if (result._header_.success) {
                let userInfoData = { userName: result.user.name, userId: result.user.id, mode: 'employee', tokenData: result };
                parent.Main.PageApp.UserInfo.userName = userInfoData.userName;
                parent.Main.PageApp.UserInfo.userMode = userInfoData.mode;
                CommonUtil.setLocalStorage('UserInfo', JSON.stringify(userInfoData));
                EcpUserLogin.doReadyLogin(result.user.id, result.user.name, result.tokenId);
            } else {
                Swal.fire({
                    icon: 'info',
                    title: Text['errorLogin'],
                    text: Text['errorLoginContent'],
                    showCloseButton: false,
                    showCancelButton: false,
                    showConfirmButton: true,
                    confirmButtonText: Text['OK'],
                });
            }
        });
    },

    doReadyLogin(userId, name, tokenId) {
        HttpQuery.doRequest('/openapi/user/checkToken', {
            "_header_": {
                "tokenId": tokenId
            }
        }, function (result) {
            if (result._header_.success) {
                HttpQuery.doRequest('/openapi/copilot/getCopilotInfo', {
                    userId: userId,
                    tokenId: tokenId,
                    mode: 'API'
                }, function (result) {
                    CommonUtil.setLocalStorage('QbiCopilotInfo', JSON.stringify(result));
                    if (result.enable) {
                        parent.Main.PageApp.isLogin = true;
                        if(!(result.iconUrl == "" || result.iconUrl == null)){
                            let userIcon = Config.ECP_URL + '/' + result.iconUrl;
                            parent.Main.PageApp.UserInfo.userIcon = userIcon;
                        }
                        parent.Main.PageApp.UserInfo.userName = name;
                        parent.Main.PageApp.showLogout = true;
                        if (result.isReady) {
                            Login._loginSuccess();
                        } else {
                            parent.Main.doNotReady();
                        }
                    }
                });
            } else {
                CommonUtil.deleteLocalStorage('UserInfo');
            }
        });
    },

    doLogout(tokenId) {
        let data = JSON.stringify({
            "_header_": {
                "tokenId": tokenId
            }
        });
        $.ajax({
            url: Config.ECP_URL + '/openapi/qs/token/discard',
            type: "post",
            data: data,
            contentType: "application/json",
            async: true,
            success: function (data, textStatus, request) {
                console.log('[EcpUserLogin] logout success.');
            },
            error: function (jqXHR, textStatus, errorThrown) {
                console.error('[EcpUserLogin] logout error');
            },
        });
    }
}