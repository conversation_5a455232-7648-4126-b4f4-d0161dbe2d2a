var MessageGptText = {

    template: `
    <div class="MessageGptText">
            <div class="MessageText">
                <font class="Copy-{copyId} WordBreakAll">
                    {content}
                </font>

                <div style="padding-top: 10px;">
                    <font style="color:rgb(0, 88, 176)">{dataSourceText}</font>
                    <br>
                    <div id="source">
                        {sources}
                    </div>
                    <div class="ViewMoreContainer">
                        <span class="ViewMoreText">
                            <a href="#{messageId}"
                                onclick="ChatEvent.onViewMoreClick(this,'{inputQuestion}','{messageId}');">{viewMore}</a>
                        </span>
                    </div>
                    <div style="display: inline-block;width:100%"><font style="color:rgb(234, 107, 102); font-size: smaller;">{gptAlert}</font></div>
                </div>
            </div>
            <div class="MessageText_Satis">
                {timestamp}
                <div class="satisfactionBar animate__animated animate__fadeInUp">
                    <image id="{messageId}-1" src="../../image/dislike.png" class="satisfactionBtnDislike" title="{satisfactDislikeText}"
                    onclick="ChatEvent.onSatisfactionClick(this,'{messageId}','-1');"></image>
                    <image id="{messageId}0" src="../../image/ok.png" class="satisfactionBtnOk" title="{satisfactOKText}"
                    onclick="ChatEvent.onSatisfactionClick(this,'{messageId}','0');"></image>
                    <image id="{messageId}1" src="../../image/like.png" class="satisfactionBtnLike" title="{satisfactLikeText}"
                    onclick="ChatEvent.onSatisfactionClick(this,'{messageId}','1');"></image>
                    <image src="../../image/copy_1.png" class="satisfactionBtnCopy" title="{satisfactCopyText}"
                    onclick="ChatEvent.onCopyClick(this,'{copyString}');"></image>
                    <img class="SpeakMessageVolume"  title="{SpeakMessageVolumeHint}"  src="../../image/SingalSpeech_.png" onclick="WebSpeechSynthesis.speak('{speechSynthesisString}')">
                </div>
            </div>
            </br>
    </div>
    <div>
        {relateds}
    </div>
    `,

    create(args) {
        let html = MessageGptText.template;
        let timestamp = Chat.Chat_HistoryTimestamp || CommonUtil.formatStringDate(new Date()); 
        //GPT專屬格式資料清整
        if (args?.mode === "GPT") {
          html = MessageGptText.doReplaceViewMore(html);
          args = MessageGptText.doParseArgs(args);
        }
        html = html.replaceAll('{content}', args.content);
        html = html.replaceAll('{messageId}', args.messageId);

        let copyId = CommonUtil.getRandomUuid();
        html = html.replaceAll('{copyId}', copyId);
        html = html.replaceAll('{copyString}', copyId);

        let copyContent = CommonUtil.stripHTML(args.content, true);
        MessageController.TotalMessage_Synthesis += copyContent;

        html = html.replaceAll('{timestamp}', timestamp);

        html = html.replaceAll('{SpeakMessageVolumeHint}', Text['SpeakMessageVolumeHint']);
        html = html.replaceAll('{viewMore}', Text['viewMore']);
        html = html.replaceAll('{inputQuestion}', args.inputQuestion.replace(/'/g, "\\'"));
        html = html.replace('{dataSourceText}', Text['dataSourceText']);
        html = html.replace('{gptAlert}', Chat.PageApp.ServerConfig?.gptGenerateText==null?Text['gptAlert']:Chat.PageApp.ServerConfig.gptGenerateText);
        if (args?.mode === "GPT") {
            html = html.replace(Chat.PageApp.ServerConfig?.gptGenerateText, "");
            html = html.replace("{sources}", args.sources);
            // html = html.replace("{relateds}", args.relateds);
            html = html.replace("{relateds}","");
        } else {
            html = html.replace("{sources}", MessageGptText.getSources(args.sources));
            // html = html.replace("{relateds}",MessageGptText.getRelateds(args.relateds));
            html = html.replace("{relateds}","");
        }
        html = html.replace('{satisfactLikeText}', Text['satisfactLikeText']);
        html = html.replace('{satisfactOKText}', Text['satisfactOKText']);
        html = html.replace('{satisfactDislikeText}', Text['satisfactDislikeText']);
        html = html.replace('{satisfactCopyText}', Text['satisfactCopyText']);
        html = html.replaceAll('{speechSynthesisId}', copyId);
        html = html.replaceAll('{speechSynthesisString}', CommonUtil.stripHTML(MessageController.TotalMessage_Synthesis,true));

        return html;
    },

    getSources(sources) {
        let result = "";
        let template = `
        <a href="#{id}" onclick="ChatEvent.onSourceClick('{id}','{source}','{type}', '{mark}')">[{count}] {content}</a>
        <br>`;
        let templateDownload = `
        <a href="#{id}" onclick="ChatEvent.onSourceClick('{id}','{source}','{type}')"><img src="../../image/file.png" width="15px"></a>`;
        for (let i = 0; i < sources.length; i++) {
            let item = sources[i];
            let resultItem = template;
            resultItem = resultItem.replace('{count}', item.count);
            if ('file' == item.type || 'document' === item.type) {
                if ('file' == item.type) {
                    resultItem = resultItem.replaceAll('{id}', item.knowledgeId);
                    resultItem = resultItem.replace('{type}', 'km'); //點選開啟知識預覽
                } else if ('document' == item.type) {
                    resultItem = resultItem.replaceAll('{id}', item.id);
                    resultItem = resultItem.replace('{type}', 'document'); //點選開啟知識預覽
                }
                let download = templateDownload;
                download = download.replaceAll('{id}', item.id);
                download = download.replace('{source}', item.content);
                download = download.replace('{type}', item.type);
                resultItem = resultItem.replaceAll('{source}', item.content);
                resultItem = resultItem.replaceAll('{content}', item.content + download);
            } else {
                resultItem = resultItem.replaceAll('{id}', item.knowledgeId);
                resultItem = resultItem.replace('{type}', 'km'); //點選開啟知識預覽
                resultItem = resultItem.replaceAll('{source}', item.content);
                resultItem = resultItem.replaceAll('{content}', item.content);
                resultItem = resultItem.replace('{mark}', escape(item.page_content))

            }
            result += resultItem;
        }
        return result;
    },

    getRelateds(relateds) {
        let result = "";
        let template = `<div class="GptRelated" onclick="ChatEvent.onRelatedClick('{content}')" title="{answer}">{content}</div>`;
        let template_noAnswer = `<div class="GptRelated" onclick="ChatEvent.onRelatedClick('{content}')">{content}</div>`;
        if (relateds.length > 0) {
            MessageController.TotalMessage_Synthesis += Text["relatedPreWord"];
        }
        for (let i = 0; i < relateds.length; i++) {
            let item = relateds[i];
            let isHasAnswer = item.hasOwnProperty('follow-up_answer');
            let resultItem = isHasAnswer ? template : template_noAnswer;
            resultItem = resultItem.replaceAll('{content}', item['follow-up_question']);
            MessageController.TotalMessage_Synthesis += item['follow-up_question'];
            if (isHasAnswer) {
                resultItem = resultItem.replaceAll('{answer}', item['follow-up_answer']);
            }
            result += resultItem;
        }
        return result;
    },
    doParseArgs(args) {
      args.ans.map((item, index) => {
        let isSatis = item?.isSatis;
        item = item.type === "QuickReply" ? item.QuickReply : item;
        let contentTemp = MessageGptText.doParseArgsItem(item, item.type);
        switch (contentTemp.type) {
          case "content":
            args.content = MessageGptText.doReplaceLink(contentTemp.text);
            break;
          case "sources":
            args.sources = MessageGptText.doTransGptSources(contentTemp);
            break;
          case "relateds":
            args.relateds = MessageGptText.doTransGptRelateds(contentTemp);
            break;
        }
        MessageGptText.doHandleWebiviewUrl(item);
        MessageGptText.doHandleQuickreply(item, isSatis);
      });
      return args;
    },
    doParseArgsItem(item) {
      let contentTemp = item;
      switch (item.type) {
        case "Text":
          contentTemp.type = "content";
          break;
        default:
          let itemTitle = item?.FQACardColumn[0]?.title;
          if (itemTitle) {
            if (itemTitle.includes("資料來源")) {
              contentTemp.type = "sources";
            } else if (itemTitle.includes("相關問題")) {
              contentTemp.type = "relateds";
            }
          }
          break;
      }
      return contentTemp;
    },
    doTransGptSources(contentTemp) {
      let result = "";
      let template = `
          <a href="#{id}" onclick="ChatEvent.onSourceClick('{id}','{WebViewTitle}','{type}', '{mark}')">{content}</a>
          <br>`;
      for (let i = 0; i < contentTemp.FQACardColumn.length; i++) {
        let sourcesitems = contentTemp.FQACardColumn[i];
        for (let j = 0; j < sourcesitems.FQACardAnswer.length; j++) {
          let sourcesitem = sourcesitems.FQACardAnswer[j];
          let resultItem = template;
          resultItem = resultItem.replaceAll("{id}", MessageGptText.doFindSourceId(sourcesitem.FName));
          resultItem = resultItem.replace(
            "{content}",
            sourcesitem.FDisplayText || sourcesitem.FShowText
          );
          resultItem = resultItem.replace(
            "{WebViewTitle}",
            MessageGptText.removeNumberTags(sourcesitem.FDisplayText || sourcesitem.FShowText)
          );
          resultItem = resultItem.replace("{type}", "km");
          result += resultItem;
        }
      }
      return result;
    },
    doTransGptRelateds(contentTemp) {
      let result = "";
      let template = `<div class="GptRelated" onclick="ChatEvent.onRelatedClick('{FShowText}')" >{FDisplayText}</div>`;
      if (contentTemp.length > 0) {
        MessageController.TotalMessage_Synthesis += Text["relatedPreWord"];
      }
      for (let i = 0; i < contentTemp.FQACardColumn.length; i++) {
        let relatedsItems = contentTemp.FQACardColumn[i];
        for (let j = 0; j < relatedsItems.FQACardAnswer.length; j++) {
          let relatedsItem = relatedsItems.FQACardAnswer[j];
          let resultItem = template;
          resultItem = resultItem.replace(
            "{FDisplayText}",
            relatedsItem.FDisplayText || relatedsItem.FShowText
          );
          resultItem = resultItem.replace("{FShowText}", relatedsItem.FShowText);
          // resultItem = resultItem.replace("{mark}", escape(relatedsItem.page_content));
          result += resultItem;
        }
      }
      return result;
    },
    doHandleWebiviewUrl(content) {
      //處理WebView
      if (content.QuickReply?.WebViewUrl) {
        QbiAnswer["WebView"](content.QuickReply.WebViewUrl);
      }
    },
    doHandleQuickreply(content, isSatis) {
      //處理QuickReply
      if (!isSatis) {
        if (content?.quick_reply_items) {
          QbiAnswer["QuickReply"](content);
        }
        if (content?.QuickReply?.quick_reply_items) {
          QbiAnswer["QuickReply"](content.QuickReply);
        }
      }
    },
    doReplaceLink(text) {
      return text.replace(
        /\[\[link url="[^"]*sourceId=([^&"]+)[^"]*sourceName=([^&"]+)[^"]*"\](\d+)\[\/link\]\]/g,
        (match, sourceId, sourceName, label) =>
          `<a href="#${sourceId}" onclick="ChatEvent.onSourceClick('${sourceId}','${decodeURIComponent(sourceName)}','km', '{mark}')">[${label}]</a>`
      );
    },
    
    doReplaceViewMore(html) {
      return html.replace(
        /<div class="ViewMoreContainer"/,
        '<div class="ViewMoreContainer" style="display: none;"'
      );
    },
    doFindSourceId(url) {
      // 正則表達式匹配 sourceId 的值
      const regex = /[?&]sourceId=([^&]+)/;
      const match = url.match(regex);
      return match ? match[1] : null;
    },
    removeNumberTags(input) {
      return input.replace(/\[\d+\]/g, '');
    }
}
