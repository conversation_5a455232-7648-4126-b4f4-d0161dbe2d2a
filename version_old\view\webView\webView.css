.closeIcon {
    width: 34px;
}
.close {
    position: fixed;
    right: 5px;
    cursor: pointer;
}
.webviewback {
    width: 28px;
    position: fixed;
    left: 5px;
    cursor: pointer;
}
.webviewbackIcon {
    width: 28px;
}

.mainView {
    width: 100%;
    border: 0px;
    padding: 10px;
    scrollbar-gutter: stable;
    overflow: auto;
    height: 95vh;
}

.ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.mainWebView {
    height: 99.5vh;
    width: 100%;
    border: 0px;
}

.navBar {
    background-color: rgb(139, 168, 217);
    position: fixed;
    top: 0px;
    left: 0px;
    width: 100%;
    z-index: 99999;
}

.title {
    font-size: 15px;
    color: white;
    font-weight: bold;
}

.mark {
    background-color: yellow;
}

body {
    overflow: hidden;
}

.question {
    color: rgb(226, 76, 75);
    font-size: 20px;
    font-weight: bold;
}

mark {
    background-color: #FFF000!important;
  }