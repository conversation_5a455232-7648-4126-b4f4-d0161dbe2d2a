var ActionManager = {
    
    isDoAddHistory: false,
    doInit() {
        ThirdPartyTool.includeJS('./action/service/ActionCopilot.js');
        ThirdPartyTool.includeJS('./action/service/ActionFunction.js');
        ThirdPartyTool.includeJS('./action/service/ActionQbiBotPlus.js');

        setTimeout(function () {
            ActionManager['get_km_answer'] = ActionCopilot.execute;
            ActionManager['do_function'] = ActionFunction.execute;
            ActionManager['do_QbiBotPlus'] = ActionQbiBotPlus.execute;
            console.log('[ActionManager-doInit] init success.');
        }, 1000);
    },

    doShowIntentsQuestion(topN_Result) {
        // 顯示多意圖來源
        if (topN_Result.hasOwnProperty('questions')) {
            let questions = topN_Result.questions;
            if (questions.length > 1) {
                for (let i = 0; i < questions.length; i++) {
                    let question = questions[i];
                    let content = Text['searching'] + question;
                    MessageController.doAddMessage(MessageController.notice(content), MessageController.LEFT);
                }
                ActionManager.showWaitingMessage();
            }
        }
    },

    //等待訊息
    showWaitingMessage() {
        try {
            Chat.PageApp.NoticeContent = {
                noticeId: CommonUtil.getRandomUuid(),
                content: Text['answering']
            }
            MessageController.doAddMessage(MessageController.notice(Chat.PageApp.NoticeContent), MessageController.LEFT);

        } catch {
            console.warn('[ActionManager - showWaitingMessage] format wrong..');
            Chat.PageApp.LOCK_SEND = false;
        }
    },
    //等待訊息
    handleErrorState(result) {
        let content ;
        if(result?.httpCode===429 ||result?.httpCode===400 ){
            let showContent = {
                needSatisfaction: false,
                needSpeechSynthesis: true,
                content: result?.httpCode===429?Text['chatError429']:Text['chatError400'],
                messageId: result?.messageId
            }
            content = MessageController.text(showContent);
            Chat.Chat_answer = JSON.stringify({ text: result?.httpCode===429?Text['chatError429']:Text['chatError400'], type: "text" });
            Chat.Chat_dataSource = "error";
        }else{
            let errorContent;
            switch (result.errorType) {
                case 'inhibit':
                    errorContent = Text['error_inhibit'];
                    break;
            }
            content = MessageController.text(errorContent);
            Chat.Chat_answer = JSON.stringify({ text: errorContent, type: "text" });
            Chat.Chat_dataSource = "noAnswer";
        }
        Chat.Chat_answerType = "text";
        MessageController.doAddMessage(content, MessageController.LEFT);
        Chat.PageApp.LOCK_SEND = false;
        return;
    },
}