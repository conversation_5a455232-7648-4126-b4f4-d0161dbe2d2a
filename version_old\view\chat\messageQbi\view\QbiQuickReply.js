var QbiQuickReply = {
  /**
   * @desc Store QuickReply Swiper. Default value is null.
   * @type {any}
   * @default null
   */
  quickReplyPool: null,

  // QuickReply HTML 模板
  templates: {
    verticalBackground: `
      <div class='QuickreplySlide' style='color:{color} !important; cursor:pointer; background-color: unset !important; border-color:{borderColor} !important;'>
        <img class='QuickReply_img' src='{imgUrl}' style='{imgDisplay}' alt='Quickly Reply'/>
        {entity}
      </div>
    `,
    verticalBorder: `
      <div class='QuickreplySlide' style='color:{color} !important; cursor:pointer; background-color: unset !important; border-color:{borderColor} !important;'>
        <img class='QuickReply_img' src='{imgUrl}' style='{imgDisplay}' alt='Quickly Reply'/>
        {entity}
      </div>
    `,
    horizontalBackground: `
      <div class='swiper-slide QuickreplySlide' style='width: fit-content;color:{color} !important; cursor:pointer; background-color: unset !important; border-color:{borderColor} !important;'>
        <img class='QuickReply_img' src='{imgUrl}' style='{imgDisplay}' alt='Quickly Reply'/>
        {entity}
      </div>
    `,
    horizontalBorder: `
      <div class='swiper-slide QuickreplySlide' style='width: fit-content;color:{color} !important; background-color: unset !important; border-color:{borderColor} !important;'>
        <img class='QuickReply_img' src='{imgUrl}' style='{imgDisplay}' alt='Quickly Reply'/>
        {entity}
      </div>
    `,
  },

  // Create QuickReply button
  create: function (content) {
    $("#ToolZone").show();
    switch (Config.QUICKREPLY_DIRECTION) {
      case "Vertical":
        $("#QuickReply").addClass("QuickReply_Vertical");
        break;
      case "Horizontal":
        $("#QuickReply").removeClass("QuickReply_Vertical");
        break;
      default:
        break;
    }

    let quick_reply_item = content.hasOwnProperty("QuickReply")
      ? content.QuickReply.quick_reply_items
      : content.quick_reply_items;

    for (let i = 0; i < quick_reply_item.length; i++) {
      let colorIndex;
      let entity = "";
      let item = quick_reply_item[i];
      let imgUrl = item.ImageUrl || item.Url || "";
      let display = item.DisplayText || item.ShowText;

      // 隨機顏色
      if (Config.QUICKREPLY_RANDOM_COLOR) {
        let arr = new Uint8Array(1);
        const crypto = window.crypto || window.msCrypto;
        crypto.getRandomValues(arr);
        let randomNum = arr[0] * Math.pow(2, -8);
        colorIndex = Math.floor(
          randomNum * Config.QUICKREPLY_BUTTON_COLOR.length
        );
      } else {
        colorIndex = i % Config.QUICKREPLY_BUTTON_COLOR.length;
      }

      switch (item.Option) {
        case "QA":
        case "Option":
          card_code = encodeURIComponent(
            JSON.stringify({
              FCode: item.Code,
              FDisplayText: display,
              FShowText: item.ShowText,
            })
          );
          entity = `
            <div class='QuickReply_div' style="background-color:{backgroundColor}; border:{borderColor} solid; color:{color};"
                 tabindex="0" onkeypress="ChatEvent.onAnswerButtonClick('Option','${card_code}');" onclick="ChatEvent.onAnswerButtonClick('Option','${card_code}');">
              ${item.ShowText}
            </div>`;
          if (Config.QUICKREPLY_OPEN_CHANGE) {
            if (Config.QUICKREPLY_RANDOM_COLOR && Config.QUICKREPLY_BUTTON_COLOR.length > 0) {
              if (Config.QUICKREPLY_BUTTON_STYLE === "background") {
                let color = Config.QUICKREPLY_BUTTON_COLOR[colorIndex];
                entity = entity
                  .replace("{backgroundColor}", color)
                  .replace("{borderColor}", color)
                  .replace("{color}", "white");
              } else if (Config.QUICKREPLY_BUTTON_STYLE === "border") {
                let color = Config.QUICKREPLY_BUTTON_COLOR[colorIndex];
                entity = entity
                  .replace("{backgroundColor}", "white")
                  .replace("{borderColor}", color)
                  .replace("{color}", color);
              }
            }
          }
          break;
        case "Url":
          entity = `
            <a class='swiper_li' href='${item.Code}' target='_blank' tabindex="0">
              ${item.ShowText}
            </a>`;
          break;
      }

      QbiQuickReply.appendBtn(
        Config.QUICKREPLY_DIRECTION,
        entity,
        colorIndex,
        imgUrl
      );
    }

    if (Config.QUICKREPLY_DIRECTION == "Horizontal") {
      QbiQuickReply.quickReplyPool.slideTo(0);
    }

    QbiQuickReply.refreshMessageListLayout();
  },

  // Create QuickReply pool
  initQuickReplyPool: function () {
    switch (Config.QUICKREPLY_DIRECTION) {
      case "Vertical":
        QbiQuickReply.quickReplyPool = $("#QuickReply_Container");
        break;
      case "Horizontal":
        QbiQuickReply.quickReplyPool = new Swiper("#QuickReply_Container", {
          mode: "horizontal",
          loop: false,
          slidesPerView: "auto",
          grabCursor: true,
          spaceBetween: 10,
        });
        break;
      default:
        break;
    }
  },

  // Render QuickReply button
  appendBtn: function (direction, entity, colorIndex, imgUrl) {
    let imgDisplay = imgUrl ? "" : "display:none;";
    let color = Config.QUICKREPLY_BUTTON_COLOR[colorIndex];

    let template;
    switch (direction) {
      case "Vertical":
        template =
          Config.QUICKREPLY_BUTTON_STYLE === "background"
            ? QbiQuickReply.templates.verticalBackground
            : QbiQuickReply.templates.verticalBorder;
        break;
      case "Horizontal":
        template =
          Config.QUICKREPLY_BUTTON_STYLE === "background"
            ? QbiQuickReply.templates.horizontalBackground
            : QbiQuickReply.templates.horizontalBorder;
        break;
      default:
        return;
    }

    const html = template
      .replace("{imgUrl}", imgUrl)
      .replace("{imgDisplay}", imgDisplay)
      .replace("{entity}", entity);

    if (direction === "Vertical") {
      $("#QuickReply")[0].insertAdjacentHTML(
        "beforeend",
        DOMPurify.sanitize(html, {
          ADD_ATTR: ["onclick", "reply", "q", "onkeypress"],
        })
      );
    } else {
      QbiQuickReply.quickReplyPool.appendSlide(html);
    }
  },

  // Clear QuickReply pool
  closeQuickReplyPool: function () {
    $("#ToolZone").hide();
    QbiQuickReply.refreshMessageListLayout();
    switch (Config.QUICKREPLY_DIRECTION) {
      case "Vertical":
        $("#QuickReply").empty();
        break;
      case "Horizontal":
        QbiQuickReply.quickReplyPool.removeAllSlides();
        break;
    }
  },

  refreshMessageListLayout: function () {
    let toolHeight = 0;
    let messagelistBottom = 0;
    let DirKnowledgeHeight = 0;

    let tool = $("#ToolZone");
    let DirKnowledge = $("#DirKnowledge");
    let sendMessage = $("#sendMessage");

    DirKnowledgeHeight = parseInt(DirKnowledge.outerHeight());
    messagelistBottom = parseInt(sendMessage.outerHeight());
    if (tool.is(":visible")) toolHeight = parseInt(tool.outerHeight());

    tool.css("bottom", messagelistBottom + "px");

    const listBottom = toolHeight + messagelistBottom + DirKnowledgeHeight;
    document.getElementById(
      "messageBox"
    ).style.height = `calc(100vh - ${listBottom}px)`;
  },
};
